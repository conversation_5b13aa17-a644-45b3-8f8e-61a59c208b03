/*
  # Create market research tables

  1. New Tables
    - `market_researches`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `title` (text)
      - `project_type` (text) - 'new-idea' or 'existing-project'
      - `description` (text)
      - `industry` (text)
      - `status` (text) - 'draft', 'in-progress', 'completed'
      - `viability_score` (numeric)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `research_questions`
      - `id` (uuid, primary key)
      - `research_id` (uuid, references market_researches)
      - `question` (text)
      - `answer` (text)
      - `order_index` (integer)
      - `created_at` (timestamp)
    
    - `research_analysis`
      - `id` (uuid, primary key)
      - `research_id` (uuid, references market_researches)
      - `section` (text) - 'dashboard', 'strategic', 'mvp', etc.
      - `analysis_data` (jsonb)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to manage their own data
*/

-- Create market_researches table
CREATE TABLE IF NOT EXISTS market_researches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  title text NOT NULL,
  project_type text NOT NULL CHECK (project_type IN ('new-idea', 'existing-project')),
  description text NOT NULL,
  industry text,
  status text NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'in-progress', 'completed')),
  viability_score numeric CHECK (viability_score >= 0 AND viability_score <= 10),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create research_questions table
CREATE TABLE IF NOT EXISTS research_questions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  research_id uuid REFERENCES market_researches(id) ON DELETE CASCADE,
  question text NOT NULL,
  answer text,
  order_index integer NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Create research_analysis table
CREATE TABLE IF NOT EXISTS research_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  research_id uuid REFERENCES market_researches(id) ON DELETE CASCADE,
  section text NOT NULL,
  analysis_data jsonb NOT NULL DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE market_researches ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE research_analysis ENABLE ROW LEVEL SECURITY;

-- Create policies for market_researches
CREATE POLICY "Users can view own market researches"
  ON market_researches
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own market researches"
  ON market_researches
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own market researches"
  ON market_researches
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own market researches"
  ON market_researches
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create policies for research_questions
CREATE POLICY "Users can view own research questions"
  ON research_questions
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_questions.research_id 
      AND market_researches.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create own research questions"
  ON research_questions
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_questions.research_id 
      AND market_researches.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own research questions"
  ON research_questions
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_questions.research_id 
      AND market_researches.user_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_questions.research_id 
      AND market_researches.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own research questions"
  ON research_questions
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_questions.research_id 
      AND market_researches.user_id = auth.uid()
    )
  );

-- Create policies for research_analysis
CREATE POLICY "Users can view own research analysis"
  ON research_analysis
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_analysis.research_id 
      AND market_researches.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create own research analysis"
  ON research_analysis
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_analysis.research_id 
      AND market_researches.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own research analysis"
  ON research_analysis
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_analysis.research_id 
      AND market_researches.user_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_analysis.research_id 
      AND market_researches.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own research analysis"
  ON research_analysis
  FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM market_researches 
      WHERE market_researches.id = research_analysis.research_id 
      AND market_researches.user_id = auth.uid()
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_market_researches_user_id ON market_researches(user_id);
CREATE INDEX IF NOT EXISTS idx_market_researches_status ON market_researches(status);
CREATE INDEX IF NOT EXISTS idx_research_questions_research_id ON research_questions(research_id);
CREATE INDEX IF NOT EXISTS idx_research_questions_order ON research_questions(research_id, order_index);
CREATE INDEX IF NOT EXISTS idx_research_analysis_research_id ON research_analysis(research_id);
CREATE INDEX IF NOT EXISTS idx_research_analysis_section ON research_analysis(research_id, section);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_market_researches_updated_at
  BEFORE UPDATE ON market_researches
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_research_analysis_updated_at
  BEFORE UPDATE ON research_analysis
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();