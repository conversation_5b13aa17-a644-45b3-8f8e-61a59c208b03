import { AIService } from './aiService';

export interface GeneratedQuestion {
  question: string;
  category: string;
  importance: 'high' | 'medium' | 'low';
  reasoning?: string;
}

export interface QuestionGenerationResult {
  questions: GeneratedQuestion[];
  reasoning: string;
}

const questionGeneratorPrompt = `You are a business consultant helping to understand a project idea in detail.

Your role is to generate clarifying questions about the PROJECT CONCEPT itself to better understand:
1. What the project actually is and does
2. The core functionality and features
3. The creator's vision and goals
4. The problem it aims to solve (from the creator's perspective)
5. Technical or operational requirements
6. Project scope and boundaries
7. Unique aspects or differentiators the creator envisions

Generate 5-15 clarifying questions that are:
- About the PROJECT IDEA itself, not market research
- Questions the project creator would know the answers to
- Focused on understanding the concept, functionality, and vision
- Specific to the project description provided
- Designed to clarify what wasn't clear from the initial description

For each question, categorize it as:
- Project Concept
- Functionality & Features
- Target Users (from project perspective)
- Technical Requirements
- Project Scope
- Vision & Goals

Prioritize questions as high, medium, or low importance based on how much they help understand the project concept.`;

const questionSchema = {
  type: 'object',
  properties: {
    questions: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          question: { type: 'string' },
          category: { type: 'string' },
          importance: { 
            type: 'string',
            enum: ['high', 'medium', 'low']
          }
        },
        required: ['question', 'category', 'importance']
      }
    },
    reasoning: { type: 'string' }
  },
  required: ['questions', 'reasoning']
};

export class QuestionGeneratorService {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService({
      section: 'question_generation',
      useTools: false,
      responseFormat: 'json',
      schema: questionSchema,
      temperature: 0.8
    });

    // Ensure we're using the question generation section
    this.aiService.switchSection('question_generation', {
      useTools: false,
      responseFormat: 'json',
      schema: questionSchema,
      temperature: 0.8
    });
  }

  async generateQuestions(
    projectType: 'new-idea' | 'existing-project',
    description: string,
    industry?: string
  ): Promise<QuestionGenerationResult> {
    const prompt = `You are a market research consultant. Generate strategic questions for comprehensive business analysis.

PROJECT TYPE: ${projectType === 'new-idea' ? 'New Business Idea' : 'Existing Project/Business'}
PROJECT DESCRIPTION: "${description}"
${industry ? `INDUSTRY: ${industry}` : ''}

TASK: Generate between 5-15 clarifying questions to better understand this PROJECT CONCEPT.

REQUIREMENTS:
- Questions must be about the PROJECT IDEA itself, not market research
- Focus on understanding what the project IS and what it DOES
- Ask about functionality, features, vision, and goals
- Questions should clarify aspects that weren't clear from the description
- Ask about the creator's vision and intentions for the project
- Focus on project concept, scope, and technical/operational aspects
- For new ideas: understand the concept, functionality, and intended solution
- For existing projects: understand current state, planned changes, and vision

IMPORTANT: 
- Ask ONLY about things the project creator would know about their own idea
- DO NOT ask about market size, competitors, pricing, or market research data
- Focus on understanding the PROJECT CONCEPT, not analyzing the market
- Questions should help clarify what the project is, how it works, and what the creator envisions
- Avoid questions about external market factors

Provide exactly the number of questions needed (5-15) based on project complexity. Each question should help understand the project concept better.`;


    try {
      const response = await this.aiService.query(prompt);
      
      if (response.type === 'text') {
        // Try to parse JSON from text response
        try {
          const parsed = JSON.parse(response.text);
          return parsed;
        } catch {
          // Fallback to default questions if parsing fails
          return this.getDefaultQuestions(projectType);
        }
      }
      
      return JSON.parse(response.text);
    } catch (error) {
      console.error('Error generating questions:', error);
      // Return default questions as fallback
      return this.getDefaultQuestions(projectType);
    }
  }

  private getDefaultQuestions(projectType: 'new-idea' | 'existing-project'): QuestionGenerationResult {
    const baseQuestions: GeneratedQuestion[] = [
      {
        question: "¿Cuál es la funcionalidad principal de tu proyecto y cómo funciona?",
        category: "Project Concept",
        importance: "high"
      },
      {
        question: "¿Qué problema específico resuelve tu proyecto y cómo lo aborda?",
        category: "Project Concept",
        importance: "high"
      },
      {
        question: "¿Quién es el usuario ideal que usaría tu proyecto y en qué situaciones?",
        category: "Target Users",
        importance: "high"
      },
      {
        question: "¿Cuáles son las características o funcionalidades clave que debe tener tu proyecto?",
        category: "Functionality & Features",
        importance: "high"
      },
      {
        question: "¿Qué hace único o diferente a tu proyecto comparado con lo que ya existe?",
        category: "Vision & Goals",
        importance: "medium"
      },
      {
        question: "¿Hay algún requisito técnico o limitación específica que debemos considerar?",
        category: "Technical Requirements",
        importance: "medium"
      }
    ];

    if (projectType === 'existing-project') {
      baseQuestions.push({
        question: "¿Qué aspectos de tu proyecto actual quieres mejorar o cambiar?",
        category: "Project Scope",
        importance: "high"
      });
    } else {
      baseQuestions.push({
        question: "¿Cuál es tu visión a largo plazo para este proyecto?",
        category: "Vision & Goals",
        importance: "high"
      });
    }

    return {
      questions: baseQuestions,
      reasoning: "Estas son preguntas fundamentales que ayudan a entender el concepto del proyecto, su funcionalidad, y la visión del creador, sin entrar en análisis de mercado."
    };
  }
}