import { AIService } from './aiService';
import { DatabaseService } from '../supabase';
import { responseSchemas } from './systemPrompts';

export interface GoToMarketProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: string[];
  isGenerating: boolean;
  error?: string;
}

export class GoToMarketService {
  private researchId: string;
  private projectData: any;
  private onProgress?: (progress: GoToMarketProgress) => void;

  constructor(researchId: string, projectData: any, onProgress?: (progress: GoToMarketProgress) => void) {
    this.researchId = researchId;
    this.projectData = projectData;
    this.onProgress = onProgress;
  }

  async generateCompleteGoToMarketStrategy(): Promise<void> {
    const steps = [
      'roadmap',
      'target_market',
      'value_proposition',
      'pricing_strategy',
      'marketing_plan',
      'sales_strategy',
      'growth_projection',
      'kpis',
      'summary'
    ];

    const completedSteps: string[] = [];

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: true
        });

        // Generate analysis for current step
        await this.generateAnalysis(step);
        
        // Mark step as completed
        completedSteps.push(step);
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: false
        });
      }

      // Final progress update
      this.updateProgress({
        currentStep: 'completed',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false
      });

    } catch (error) {
      this.updateProgress({
        currentStep: 'error',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async generateAnalysis(analysisType: string): Promise<any> {
    let aiService: AIService;
    let prompt: string;
    let schema: any;

    // Configure AI service based on analysis type
    switch (analysisType) {
      case 'roadmap':
        aiService = new AIService({
          section: 'gtm_roadmap',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.gtmRoadmap,
          temperature: 0.7
        });
        prompt = await this.buildRoadmapPrompt();
        schema = responseSchemas.gtmRoadmap;
        break;

      case 'target_market':
        aiService = new AIService({
          section: 'gtm_target_market',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.gtmTargetMarket,
          temperature: 0.7
        });
        prompt = await this.buildTargetMarketPrompt();
        schema = responseSchemas.gtmTargetMarket;
        break;

      case 'value_proposition':
        aiService = new AIService({
          section: 'gtm_value_proposition',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.gtmValueProposition,
          temperature: 0.7
        });
        prompt = await this.buildValuePropositionPrompt();
        schema = responseSchemas.gtmValueProposition;
        break;

      case 'pricing_strategy':
        aiService = new AIService({
          section: 'gtm_pricing_strategy',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.gtmPricingStrategy,
          temperature: 0.7
        });
        prompt = await this.buildPricingStrategyPrompt();
        schema = responseSchemas.gtmPricingStrategy;
        break;

      case 'marketing_plan':
        aiService = new AIService({
          section: 'gtm_marketing_plan',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.gtmMarketingPlan,
          temperature: 0.7
        });
        prompt = await this.buildMarketingPlanPrompt();
        schema = responseSchemas.gtmMarketingPlan;
        break;

      case 'sales_strategy':
        aiService = new AIService({
          section: 'gtm_sales_strategy',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.gtmSalesStrategy,
          temperature: 0.7
        });
        prompt = await this.buildSalesStrategyPrompt();
        schema = responseSchemas.gtmSalesStrategy;
        break;

      case 'growth_projection':
        aiService = new AIService({
          section: 'gtm_growth_projection',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.gtmGrowthProjection,
          temperature: 0.7
        });
        prompt = await this.buildGrowthProjectionPrompt();
        schema = responseSchemas.gtmGrowthProjection;
        break;

      case 'kpis':
        aiService = new AIService({
          section: 'gtm_kpis',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.gtmKPIs,
          temperature: 0.7
        });
        prompt = await this.buildKPIsPrompt();
        schema = responseSchemas.gtmKPIs;
        break;

      case 'summary':
        aiService = new AIService({
          section: 'gtm_summary',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.gtmSummary,
          temperature: 0.7
        });
        prompt = await this.buildSummaryPrompt();
        schema = responseSchemas.gtmSummary;
        break;

      default:
        throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    // Generate analysis
    const response = await aiService.query(prompt);
    
    // Parse response
    let analysisData;
    if (response.type === 'text') {
      try {
        analysisData = JSON.parse(response.text);
      } catch (error) {
        throw new Error(`Failed to parse ${analysisType} analysis response`);
      }
    } else {
      analysisData = JSON.parse(response.text);
    }

    // Save to database
    let contextData = '';
    
    // Add strategic analyses context
    if (Array.isArray(strategicAnalyses) && strategicAnalyses.length > 0) {
      strategicAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }
    
    // Add roadmap context if available
    if (roadmapAnalysis && roadmapAnalysis.analysis_data) {
      contextData += `\n\nGTM_ROADMAP:\n${JSON.stringify(roadmapAnalysis.analysis_data, null, 2)}`;
    }

    return `Define comprehensive target market strategy based on customer personas and market analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

BUSINESS ANALYSIS CONTEXT:${contextData}

TASK: Develop detailed target market definition and segmentation strategy.

Define target market segments:
1. PRIMARY AUDIENCE: Main customer segment with highest potential
2. SECONDARY AUDIENCE: Additional segments with significant opportunity
3. MARKET SEGMENTATION: Detailed breakdown of customer segments
4. GEOGRAPHIC FOCUS: Regional and geographic targeting strategy

For each target segment, provide:
- Segment name and description
- Market size and potential (TAM, SAM, SOM)
- Customer characteristics and demographics
- Pain points and needs
- Buying behavior and decision process
- Preferred communication channels
- Competitive landscape in this segment
- Revenue potential and priority level

Include geographic targeting:
- Initial launch markets and rationale
- Expansion markets and timeline
- Regional considerations and adaptations
- Market entry strategies by geography
- Localization requirements

Analyze market dynamics:
- Market maturity and growth trends
- Competitive intensity by segment
- Barriers to entry and success factors
- Customer acquisition challenges
- Market timing and opportunity windows

Provide targeting recommendations:
- Primary focus areas for initial launch
- Segment prioritization and resource allocation
- Market entry sequence and timing
- Customer acquisition strategies by segment
- Success metrics and validation criteria

Base target market definition on customer personas, competitive analysis, and market opportunity insights from previous analyses.`;
  }

  private async buildRoadmapPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get strategic analyses for context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const strategicAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('strategic_'))
      : [];
    
    let strategicContext = '';
    if (Array.isArray(strategicAnalyses) && strategicAnalyses.length > 0) {
      strategicAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          strategicContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create a comprehensive Go-to-Market roadmap for this business:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

STRATEGIC ANALYSIS CONTEXT:${strategicContext}

TASK: Develop a detailed Go-to-Market roadmap with phases, timelines, and key milestones.

Develop a roadmap with 4-6 phases covering:
1. PRE-LAUNCH PHASE: Market validation, product development, team building
2. BETA LAUNCH PHASE: Limited release, feedback collection, iteration
3. PUBLIC LAUNCH PHASE: Full market entry, marketing campaign, customer acquisition
4. GROWTH PHASE: Scaling operations, market expansion, feature development
5. EXPANSION PHASE: New markets, product lines, strategic partnerships

For each phase, provide:
- Phase name and duration (in weeks/months)
- Key objectives and deliverables
- Critical activities and milestones
- Success criteria and metrics
- Resource requirements
- Dependencies and prerequisites
- Risk factors and mitigation strategies

Include specific milestones such as:
- Product development checkpoints
- Market validation achievements
- Customer acquisition targets
- Revenue milestones
- Team expansion points
- Partnership agreements
- Funding rounds

Ensure the roadmap is:
- Realistic and achievable given resources
- Aligned with MVP development timeline
- Focused on customer value delivery
- Adaptable to market feedback
- Measurable with clear success criteria

Base the roadmap on insights from MVP planning, strategic framework, and business requirements identified in previous analyses.`;
  }

  async buildSummaryPrompt(): Promise<string> {
    try {
      // Safely extract project data with validation
      const { research, questions = [] } = this.projectData || {};
      
      // Validate essential data
      if (!research) {
        console.error('Missing research data for GTM summary');
        throw new Error('Missing research data for GTM summary');
      }
      
      // Safely process questions with defensive checks
      const answeredQuestions = Array.isArray(questions) 
        ? questions.filter((q: any) => q && q.answer && q.question)
                 .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
                 .join('\n\n')
        : '';

      // Get analysis data with defensive checks
      const analyses = await this.getAnalyses() || [];
      let allAnalysesContext = '';
      
      if (Array.isArray(analyses) && analyses.length > 0) {
        analyses.forEach(analysis => {
          if (analysis && analysis.section && analysis.analysis_data) {
            try {
              allAnalysesContext += `\n\n### ${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
            } catch (error) {
              console.error(`Error processing ${analysis.section} analysis:`, error);
            }
          }
        });
      }
      
      return `TASK: Create an executive summary that synthesizes all Go-to-Market strategy analyses.

Include:
1. EXECUTIVE SUMMARY: Overview of the Go-to-Market strategy and approach
2. KEY FINDINGS: Most important insights from all GTM analyses
3. STRATEGIC APPROACH: Core Go-to-Market strategy and positioning
4. TARGET MARKET: Primary customer segments and market opportunity
5. COMPETITIVE ADVANTAGES: Key differentiators and positioning strengths
6. IMPLEMENTATION ROADMAP: Critical milestones and execution priorities
7. SUCCESS FACTORS: Key elements required for Go-to-Market success
8. NEXT STEPS: Immediate actions and implementation priorities

Provide a clear, compelling summary that:
- Communicates the Go-to-Market strategy and rationale
- Highlights critical success factors and competitive advantages
- Provides actionable recommendations for execution
- Sets realistic expectations for timeline and results
- Identifies key risks and mitigation strategies

Synthesize insights from:
- Go-to-Market roadmap and timeline
- Target market definition and segmentation
- Value propositions and competitive positioning
- Pricing strategy and revenue model
- Marketing plan and channel strategy
- Sales strategy and customer acquisition
- Growth projections and scaling plan
- KPIs and performance measurement

Focus on creating a Go-to-Market overview that guides strategic decision-making, resource allocation, and execution while clearly communicating the path to market success and sustainable growth.`;
    } catch (error) {
      console.error('Error preparing GTM summary prompt:', error);
      return `Error preparing GTM summary prompt: ${error instanceof Error ? error.message : JSON.stringify(error)}`;
    }
  }

  private updateProgress(progress: GoToMarketProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}