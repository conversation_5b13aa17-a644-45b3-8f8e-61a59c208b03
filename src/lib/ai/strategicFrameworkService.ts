import { AIService } from './aiService';
import { DatabaseService } from '../supabase';
import { responseSchemas } from './systemPrompts';

export interface StrategicFrameworkProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: string[];
  isGenerating: boolean;
  error?: string;
}

export class StrategicFrameworkService {
  private researchId: string;
  private projectData: any;
  private onProgress?: (progress: StrategicFrameworkProgress) => void;

  constructor(researchId: string, projectData: any, onProgress?: (progress: StrategicFrameworkProgress) => void) {
    this.researchId = researchId;
    this.projectData = projectData;
    this.onProgress = onProgress;
  }

  async generateCompleteFramework(): Promise<void> {
    const steps = [
      'strategy',
      'swot', 
      'pestel',
      'porter',
      'game_changing',
      'introduction'
    ];

    const completedSteps: string[] = [];

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: true
        });

        // Generate analysis for current step
        await this.generateAnalysis(step);
        
        // Mark step as completed
        completedSteps.push(step);
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: false
        });
      }

      // Final progress update
      this.updateProgress({
        currentStep: 'completed',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false
      });

    } catch (error) {
      this.updateProgress({
        currentStep: 'error',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async generateAnalysis(analysisType: string): Promise<any> {
    let aiService: AIService;
    let prompt: string;
    let schema: any;

    // Configure AI service based on analysis type
    switch (analysisType) {
      case 'strategy':
        aiService = new AIService({
          section: 'strategy_analysis',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.strategyAnalysis,
          temperature: 0.7
        });
        prompt = this.buildStrategyPrompt();
        schema = responseSchemas.strategyAnalysis;
        break;

      case 'swot':
        aiService = new AIService({
          section: 'swot_analysis',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.swotAnalysis,
          temperature: 0.7
        });
        prompt = this.buildSWOTPrompt();
        schema = responseSchemas.swotAnalysis;
        break;

      case 'pestel':
        aiService = new AIService({
          section: 'pestel_analysis',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.pestelAnalysis,
          temperature: 0.7
        });
        prompt = this.buildPESTELPrompt();
        schema = responseSchemas.pestelAnalysis;
        break;

      case 'porter':
        aiService = new AIService({
          section: 'porter_analysis',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.porterAnalysis,
          temperature: 0.7
        });
        prompt = this.buildPorterPrompt();
        schema = responseSchemas.porterAnalysis;
        break;

      case 'game_changing':
        aiService = new AIService({
          section: 'game_changing_analysis',
          useTools: false, // No web research for synthesis
          responseFormat: 'json',
          schema: responseSchemas.gameChangingIdea,
          temperature: 0.8
        });
        prompt = await this.buildGameChangingPrompt();
        schema = responseSchemas.gameChangingIdea;
        break;

      case 'introduction':
        aiService = new AIService({
          section: 'strategic_introduction',
          useTools: false, // No web research for synthesis
          responseFormat: 'json',
          schema: responseSchemas.strategicIntroduction,
          temperature: 0.7
        });
        prompt = await this.buildIntroductionPrompt();
        schema = responseSchemas.strategicIntroduction;
        break;

      default:
        throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    // Generate analysis
    const response = await aiService.query(prompt);
    
    // Parse response
    let analysisData;
    if (response.type === 'text') {
      try {
        analysisData = JSON.parse(response.text);
      } catch (error) {
        throw new Error(`Failed to parse ${analysisType} analysis response`);
      }
    } else {
      analysisData = JSON.parse(response.text);
    }

    // Save to database
    await DatabaseService.saveResearchAnalysis(this.researchId, `strategic_${analysisType}`, analysisData);

    return analysisData;
  }

  private buildStrategyPrompt(): string {
    const { research, questions } = this.projectData;
    const answeredQuestions = questions.filter((q: any) => q.answer).map((q: any) => `Q: ${q.question}\nA: ${q.answer}`).join('\n\n');

    return `Conduct a comprehensive strategy analysis for this business idea:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Analyze the business strategy, framework, requirements, and revenue streams.

Use web research to gather current industry data, successful business models, and strategic frameworks relevant to this business idea.

Focus on:
1. Business Strategy: Overall approach, positioning, competitive strategy
2. Business Framework: Business model, key components, structural advantages
3. Requirement Analysis: Technical, operational, and regulatory requirements
4. Revenue Streams: Potential revenue sources and monetization strategies

Provide detailed, research-backed analysis with specific recommendations and actionable insights.`;
  }

  private buildSWOTPrompt(): string {
    const { research, questions } = this.projectData;
    const answeredQuestions = questions.filter((q: any) => q.answer).map((q: any) => `Q: ${q.question}\nA: ${q.answer}`).join('\n\n');

    return `Conduct a comprehensive SWOT analysis for this business idea:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Perform detailed SWOT analysis using current market research.

Use web research to gather:
- Industry trends and market conditions
- Competitive landscape analysis
- Market opportunities and threats
- Industry success factors and challenges

Analyze:
- STRENGTHS: Internal positive factors and competitive advantages
- WEAKNESSES: Internal limitations and areas for improvement
- OPPORTUNITIES: External factors that could benefit the business
- THREATS: External factors that could challenge the business

Provide specific, research-backed insights with strategic recommendations for each category.`;
  }

  private buildPESTELPrompt(): string {
    const { research, questions } = this.projectData;
    const answeredQuestions = questions.filter((q: any) => q.answer).map((q: any) => `Q: ${q.question}\nA: ${q.answer}`).join('\n\n');

    return `Conduct a comprehensive PESTEL analysis for this business idea:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Analyze macro-environmental factors using current research and data.

Use web research to investigate each PESTEL factor:
- POLITICAL: Government policies, regulations, political stability, trade policies
- ECONOMIC: Economic trends, inflation, market conditions, funding environment
- SOCIAL: Demographics, cultural trends, lifestyle changes, consumer behavior
- TECHNOLOGICAL: Tech advances, innovation trends, digital transformation, automation
- ENVIRONMENTAL: Sustainability trends, environmental regulations, climate impact
- LEGAL: Legal requirements, compliance, intellectual property, data protection

Focus on current and emerging trends that could impact this specific business. Provide research-backed analysis with strategic implications.`;
  }

  private buildPorterPrompt(): string {
    const { research, questions } = this.projectData;
    const answeredQuestions = questions.filter((q: any) => q.answer).map((q: any) => `Q: ${q.question}\nA: ${q.answer}`).join('\n\n');

    return `Conduct a comprehensive Porter's Five Forces analysis for this business idea:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Analyze competitive dynamics using Porter's Five Forces framework.

Use web research to analyze each force:
1. THREAT OF NEW ENTRANTS: Barriers to entry, capital requirements, economies of scale, brand loyalty
2. BARGAINING POWER OF SUPPLIERS: Supplier concentration, switching costs, forward integration
3. BARGAINING POWER OF BUYERS: Buyer concentration, price sensitivity, switching costs, backward integration
4. THREAT OF SUBSTITUTES: Substitute availability, performance comparison, switching costs
5. COMPETITIVE RIVALRY: Number of competitors, industry growth, product differentiation, exit barriers

For each force, determine the level (Low/Medium/High) and provide detailed analysis with current market examples and strategic implications.`;
  }

  private async buildGameChangingPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all previous analyses y validar que sea un array
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const strategicAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('strategic_'))
      : [];

    let previousAnalysisContext = '';
    // Validar que strategicAnalyses sea un array antes de iterar
    if (Array.isArray(strategicAnalyses)) {
      strategicAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          previousAnalysisContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create a compelling Game Changing Idea analysis based on all previous strategic research:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

PREVIOUS STRATEGIC ANALYSES:${previousAnalysisContext}

TASK: Synthesize all previous analyses to identify and articulate the core game-changing opportunity.

Focus on:
1. CORE IDEA: What makes this business idea truly transformational
2. MARKET IMPACT: Disruption potential, market capture opportunity, revenue projection
3. COMPETITIVE ADVANTAGES: Unique advantages and their sustainability
4. STRATEGIC INSIGHTS: Key insights from all analyses
5. IMPLEMENTATION PRIORITIES: Critical success factors and priorities

Create a compelling narrative that captures why this business idea could be game-changing. Use insights from Strategy, SWOT, PESTEL, and Porter's analyses to support your conclusions.

Do NOT conduct additional web research - synthesize the research already completed.`;
  }

  private async buildIntroductionPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all strategic analyses y validar que sea un array
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const strategicAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('strategic_'))
      : [];

    let allAnalysesContext = '';
    // Validar que strategicAnalyses sea un array antes de iterar
    if (Array.isArray(strategicAnalyses)) {
      strategicAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create a comprehensive introduction for the Strategic Framework analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

ALL STRATEGIC ANALYSES COMPLETED:${allAnalysesContext}

TASK: Create an executive summary that synthesizes all strategic framework analyses.

Include:
1. EXECUTIVE SUMMARY: Overview of the strategic analysis conducted
2. KEY FINDINGS: Most important insights from all analyses
3. STRATEGIC OPPORTUNITIES: Main opportunities identified
4. MAIN CHALLENGES: Key challenges and risks identified
5. OVERALL ASSESSMENT: Strategic viability and potential
6. NEXT STEPS: Recommended actions based on the analysis

Provide a clear, compelling introduction that sets context for the detailed strategic analyses and communicates the strategic value of the business opportunity.

Do NOT conduct additional web research - synthesize all completed analyses.`;
  }

  private updateProgress(progress: StrategicFrameworkProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}