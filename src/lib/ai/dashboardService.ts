import { AIService } from './aiService';
import { DatabaseService } from '../supabase';
import { responseSchemas } from './systemPrompts';

export interface DashboardProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: string[];
  isGenerating: boolean;
  error?: string;
}

export class DashboardService {
  private researchId: string;
  private projectData: any;
  private onProgress?: (progress: DashboardProgress) => void;

  constructor(researchId: string, projectData: any, onProgress?: (progress: DashboardProgress) => void) {
    this.researchId = researchId;
    this.projectData = projectData;
    this.onProgress = onProgress;
  }

  async generateCompleteDashboard(): Promise<void> {
    const steps = ['dashboard_synthesis'];
    const completedSteps: string[] = [];

    try {
      console.log('Starting dashboard generation process');
      // Update progress
      this.updateProgress({
        currentStep: 'dashboard_synthesis',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: true
      });

      // Verify research ID
      if (!this.researchId) {
        throw new Error('Missing research ID for dashboard generation');
      }
      
      // Validate project data
      if (!this.projectData) {
        throw new Error('Missing project data for dashboard generation');
      }

      // Generate dashboard synthesis
      await this.generateAnalysis('dashboard_synthesis');
      
      // Mark step as completed
      completedSteps.push('dashboard_synthesis');
      
      // Final progress update
      this.updateProgress({
        currentStep: 'completed',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false
      });
      
      console.log('Dashboard generation completed successfully');

    } catch (error) {
      console.error('Dashboard generation failed with error:', error);
      
      // Create a detailed error message
      let errorMessage = 'Failed to generate dashboard';
      if (error instanceof Error) {
        errorMessage = `${errorMessage}: ${error.message}`;
      }
      
      this.updateProgress({
        currentStep: 'error',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false,
        error: errorMessage
      });
      
      // Rethrow with more context
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Dashboard generation failed: ${JSON.stringify(error)}`);
      }
    }
  }

  private async generateAnalysis(analysisType: string): Promise<any> {
    console.log(`Starting generation of ${analysisType}`);
    
    if (analysisType !== 'dashboard_synthesis') {
      throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    const aiService = new AIService({
      section: 'dashboard_synthesis',
      useTools: false, // No web research - synthesis only
      responseFormat: 'json',
      // Use marketSizing schema as that's what we have in responseSchemas
      schema: responseSchemas.marketSizing,
      temperature: 0.7
    });

    const prompt = await this.buildDashboardSynthesisPrompt();

    // Generate analysis
    const response = await aiService.query(prompt);
    
    // Parse response
    let analysisData;
    if (response.type === 'text') {
      try {
        console.log('Parsing dashboard synthesis response');
        analysisData = JSON.parse(response.text);
      } catch (error) {
        console.error('Failed to parse dashboard synthesis JSON response:', error);
        console.error('Response text was:', response.text?.substring(0, 500) + '...');
        throw new Error(`Failed to parse dashboard synthesis response: ${(error instanceof Error) ? error.message : 'Invalid JSON'}`);
      }
    } else {
      try {
        analysisData = JSON.parse(response.text);
      } catch (error) {
        console.error('Failed to parse dashboard synthesis response from non-text response:', error);
        throw new Error(`Failed to parse dashboard synthesis from non-text response: ${(error instanceof Error) ? error.message : 'Invalid JSON'}`);
      }
    }

    // Save to database
    await DatabaseService.saveResearchAnalysis(this.researchId, 'dashboard_synthesis', analysisData);

    return analysisData;
  }

  private async buildDashboardSynthesisPrompt(): Promise<string> {
    console.log('Building dashboard synthesis prompt with project data:', JSON.stringify(this.projectData));
    
    // Declare variables outside try block for scope access
    let research = { 
      title: 'Untitled', 
      project_type: 'unknown', 
      description: 'No description available',
      industry: ''
    };
    let answeredQuestions = 'No answered questions available';
    let allAnalysesContext = '';
    
    try {
      // Validate project data structure
      if (!this.projectData) {
        throw new Error('Project data is undefined or null');
      }

      // Safely extract research and questions with proper validation
      const { research: projectResearch, questions = [] } = this.projectData || {};

      // Validate essential data
      if (!projectResearch) {
        console.error('Missing research data for dashboard synthesis');
        throw new Error('Missing research data for dashboard synthesis');
      }

      // Use the extracted research data
      research = projectResearch;
      
      // Safely process questions with defensive checks
      const projectQuestions = questions || [];
      answeredQuestions = Array.isArray(projectQuestions)
        ? projectQuestions.filter((q: any) => q && q.answer && q.question)
                 .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
                 .join('\n\n')
        : '';

      // Get ALL research analyses to provide a comprehensive picture
      const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
      
      // Log the retrieved analyses for debugging
      console.log(`Retrieved ${analyses.length} analyses for research ID: ${this.researchId}`);
      
      // Validate analyses array
      if (!Array.isArray(analyses)) {
        console.error('Invalid analyses data structure (not an array)');
        throw new Error('Invalid analyses data structure for dashboard synthesis');
      }
      
      // Organize analyses by category with defensive checks
      const strategicAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('strategic_'));
      const uspAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('usp_'));
      const mvpAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('mvp_'));
      const customerAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('customer_'));
      const competitiveAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('competitive_'));
      const financialAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('finances_'));
      const gtmAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('gtm_'));

      // Process each analysis type and add to context
      let allAnalysesContext = '';
      
      // Function to safely process analyses of any type
      const processAnalysesCategory = (analyses: any[], categoryTitle: string) => {
        if (!Array.isArray(analyses) || analyses.length === 0) return;
        
        allAnalysesContext += `\n\n## ${categoryTitle}:`;
        analyses.forEach(analysis => {
          if (analysis && analysis.section && analysis.analysis_data) {
            try {
              allAnalysesContext += `\n\n### ${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
            } catch (error) {
              console.error(`Error processing ${analysis.section} analysis:`, error);
              // Add error info but continue processing other analyses
              allAnalysesContext += `\n\n### ${analysis.section.toUpperCase()}:\n{ "error": "Data processing error" }`;
            }
          }
        });
      };
      
      // Process all analysis types with the safe function
      processAnalysesCategory(strategicAnalyses, 'STRATEGIC FRAMEWORK ANALYSES');
      processAnalysesCategory(uspAnalyses, 'USP ANALYSES');
      processAnalysesCategory(mvpAnalyses, 'MVP ANALYSES');
      processAnalysesCategory(customerAnalyses, 'CUSTOMER ANALYSES');
      processAnalysesCategory(competitiveAnalyses, 'COMPETITIVE ANALYSES');
      processAnalysesCategory(financialAnalyses, 'FINANCIAL ANALYSES');
      processAnalysesCategory(gtmAnalyses, 'GO-TO-MARKET ANALYSES');
      
      // Log analysis processing results for debugging
      console.log(`Processed analyses for dashboard synthesis: ${strategicAnalyses.length} strategic, ${uspAnalyses.length} USP, ${mvpAnalyses.length} MVP, ${customerAnalyses.length} customer, ${competitiveAnalyses.length} competitive, ${financialAnalyses.length} financial, ${gtmAnalyses.length} GTM`);

    // Close the try block
    } catch (error) {
      console.error('Error preparing dashboard synthesis prompt:', error);
      
      // Log more details about the error for debugging
      if (error instanceof Error) {
        console.error(`Error name: ${error.name}, Message: ${error.message}`);
        console.error(`Stack trace: ${error.stack}`);
      } else {
        console.error(`Unknown error type: ${typeof error}`, error);
      }
      
      // Return a minimal prompt with error information
      return `Error preparing dashboard synthesis: ${error instanceof Error ? error.message : JSON.stringify(error)}`;
    }
    
    // Now we can safely use all variables since they're defined outside the try block

    return `Create a comprehensive Dashboard synthesis that summarizes ALL completed market research analyses:

PROJECT INFORMATION:
Title: ${research?.title || 'Untitled Project'}
Type: ${research?.project_type || 'Unknown Type'}
Description: ${research?.description || 'No description available'}
Industry: ${research?.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions || 'No additional project details available'}

COMPLETE MARKET RESEARCH ANALYSES:${allAnalysesContext}

TASK: Synthesize ALL market research analyses into a comprehensive Dashboard overview.

CRITICAL REQUIREMENTS:
- Base ONLY on the research data provided above
- Do NOT invent or exaggerate any information
- Extract and synthesize REAL insights from the completed analyses
- Provide accurate summaries that reflect the actual research findings
- Ensure all metrics and data points come from the analyses provided

Create a comprehensive dashboard that includes:

1. PROJECT OVERVIEW:
   - Project title and description synthesis
   - Industry and market context
   - Project type and current status
   - Key project characteristics from research

2. VIABILITY SCORE:
   - Overall viability assessment (1-10 scale) based on all analyses
   - Justification based on strategic, financial, and market factors
   - Key factors contributing to viability score
   - Risk factors that impact viability

3. MARKET OPPORTUNITY:
   - Total Addressable Market (TAM) from financial research
   - Serviceable Addressable Market (SAM) from financial research
   - Serviceable Obtainable Market (SOM) from financial research
   - Market growth trends and opportunities identified
   - Target market size and potential from customer personas

4. UNIQUE SELLING POINTS:
   - Primary USP from USP analysis
   - Supporting USPs and value propositions
   - Competitive advantages identified
   - Differentiation factors from competitive analysis

5. FINANCIAL HIGHLIGHTS:
   - Revenue projections from financial analysis
   - Investment requirements from startup costs
   - Profitability timeline from breakeven analysis
   - Key financial metrics and KPIs
   - Funding requirements and strategy

6. CUSTOMER INSIGHTS:
   - Number of customer personas identified
   - Primary target segments from personas analysis
   - Key customer needs and pain points
   - Customer acquisition strategy from GTM analysis

7. COMPETITIVE POSITION:
   - Market position from competitive analysis
   - Key competitors identified
   - Competitive advantages and challenges
   - Market differentiation strategy

8. STRATEGIC INSIGHTS:
   - Key strategic opportunities from strategic framework
   - Main challenges and risks identified
   - Critical success factors
   - Strategic recommendations across all analyses

9. IMPLEMENTATION ROADMAP:
   - MVP timeline from MVP analysis
   - Go-to-market timeline from GTM analysis
   - Key milestones and phases
   - Resource requirements and priorities

10. SUCCESS METRICS:
    - Key performance indicators from all analyses
    - Success criteria and targets
    - Measurement framework
    - Critical metrics for tracking progress

SYNTHESIS GUIDELINES:
- Extract specific data points, metrics, and insights from the provided analyses
- Synthesize findings across different analysis areas
- Identify common themes and patterns
- Highlight contradictions or areas needing attention
- Provide balanced assessment based on all research
- Focus on actionable insights and recommendations
- Ensure consistency across all dashboard elements

The dashboard should provide a complete, accurate overview of the business opportunity based solely on the comprehensive market research conducted, without adding any external assumptions or invented data.`;
  }

  private updateProgress(progress: DashboardProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}