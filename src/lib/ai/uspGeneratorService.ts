import { AIService } from './aiService';
import { DatabaseService } from '../supabase';
import { responseSchemas } from './systemPrompts';

export interface USPGeneratorProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: string[];
  isGenerating: boolean;
  error?: string;
}

export class USPGeneratorService {
  private researchId: string;
  private projectData: any;
  private onProgress?: (progress: USPGeneratorProgress) => void;

  constructor(researchId: string, projectData: any, onProgress?: (progress: USPGeneratorProgress) => void) {
    this.researchId = researchId;
    this.projectData = projectData;
    this.onProgress = onProgress;
  }

  async generateCompleteUSPFramework(): Promise<void> {
    const steps = [
      'propositions',
      'acid_test',
      'uniqueness',
      'examples',
      'competitive_ranking',
      'introduction'
    ];

    const completedSteps: string[] = [];

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: true
        });

        // Generate analysis for current step
        await this.generateAnalysis(step);
        
        // Mark step as completed
        completedSteps.push(step);
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: false
        });
      }

      // Final progress update
      this.updateProgress({
        currentStep: 'completed',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false
      });

    } catch (error) {
      this.updateProgress({
        currentStep: 'error',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async generateAnalysis(analysisType: string): Promise<any> {
    let aiService: AIService;
    let prompt: string;
    let schema: any;

    // Configure AI service based on analysis type
    switch (analysisType) {
      case 'propositions':
        aiService = new AIService({
          section: 'usp_propositions',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.uspPropositions,
          temperature: 0.7
        });
        prompt = await this.buildPropositionsPrompt();
        schema = responseSchemas.uspPropositions;
        break;

      case 'acid_test':
        aiService = new AIService({
          section: 'usp_acid_test',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.uspAcidTest,
          temperature: 0.7
        });
        prompt = await this.buildAcidTestPrompt();
        schema = responseSchemas.uspAcidTest;
        break;

      case 'uniqueness':
        aiService = new AIService({
          section: 'usp_uniqueness',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.uspUniqueness,
          temperature: 0.7
        });
        prompt = await this.buildUniquenessPrompt();
        schema = responseSchemas.uspUniqueness;
        break;

      case 'examples':
        aiService = new AIService({
          section: 'usp_examples',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.uspExamples,
          temperature: 0.7
        });
        prompt = await this.buildExamplesPrompt();
        schema = responseSchemas.uspExamples;
        break;

      case 'competitive_ranking':
        aiService = new AIService({
          section: 'usp_competitive_ranking',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.uspCompetitiveRanking,
          temperature: 0.7
        });
        prompt = await this.buildCompetitiveRankingPrompt();
        schema = responseSchemas.uspCompetitiveRanking;
        break;

      case 'introduction':
        aiService = new AIService({
          section: 'usp_introduction',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.uspIntroduction,
          temperature: 0.7
        });
        prompt = await this.buildIntroductionPrompt();
        schema = responseSchemas.uspIntroduction;
        break;

      default:
        throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    // Generate analysis
    const response = await aiService.query(prompt);
    
    // Parse response
    let analysisData;
    if (response.type === 'text') {
      try {
        analysisData = JSON.parse(response.text);
      } catch (error) {
        throw new Error(`Failed to parse ${analysisType} analysis response`);
      }
    } else {
      analysisData = JSON.parse(response.text);
    }

    // Save to database
    await DatabaseService.saveResearchAnalysis(this.researchId, `usp_${analysisType}`, analysisData);

    return analysisData;
  }

  private async buildPropositionsPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get strategic framework data for context
    const strategicAnalyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const strategicData = Array.isArray(strategicAnalyses)
      ? strategicAnalyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('strategic_'))
      : [];
    
    let strategicContext = '';
    strategicData.forEach(analysis => {
      strategicContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
    });

    return `Develop comprehensive unique selling propositions for this business idea:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

STRATEGIC CONTEXT:${strategicContext}

TASK: Create compelling unique selling propositions that differentiate this business in the market.

Use web research to analyze:
- Current market positioning of competitors
- Successful USP examples in the industry
- Customer pain points and unmet needs
- Market trends and opportunities

Focus on:
1. PRIMARY USP: The main unique selling proposition that captures the core differentiation
2. SUPPORTING USPs: Additional value propositions that reinforce the primary USP
3. VALUE PROPOSITIONS: Specific value statements for different customer segments
4. COMPETITIVE MOATS: Sustainable advantages that protect market position

Ensure USPs are:
- Clear and memorable
- Specific and measurable
- Relevant to target customers
- Defensible against competition
- Aligned with business capabilities

Create USPs that resonate with target customers and clearly communicate why they should choose this solution over alternatives.`;
  }

  private async buildAcidTestPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get previous USP propositions
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const uspPropositions = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section === 'usp_propositions')[0]
      : undefined;
    
    let propositionsContext = '';
    if (uspPropositions) {
      propositionsContext = `\n\nUSP PROPOSITIONS:\n${JSON.stringify(uspPropositions.analysis_data, null, 2)}`;
    }

    return `Conduct a rigorous USP Acid Test analysis for the developed unique selling propositions:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

USP CONTEXT:${propositionsContext}

TASK: Evaluate each USP using the Early USP Acid Test framework.

For each USP, assess:
1. STRENGTH: How compelling and powerful is this USP?
2. UNIQUENESS: How different is this from competitors?
3. MARKET DEMAND: How much do customers want/need this?
4. SUSTAINABILITY: How long can this advantage be maintained?
5. STATUS: Overall assessment (Winning/Risky/Losing)

Evaluation criteria:
- Very High: Exceptional advantage, market-leading position
- High: Strong advantage, competitive differentiation
- Medium: Moderate advantage, some differentiation
- Low: Weak advantage, limited differentiation

Status determination:
- Winning: Strong across multiple criteria, sustainable advantage
- Risky: Mixed results, some concerns about sustainability or uniqueness
- Losing: Weak across criteria, unlikely to provide competitive advantage

Provide honest, objective assessment with specific reasoning for each rating. Include overall assessment and recommendations for strengthening weak USPs.`;
  }

  private async buildUniquenessPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get previous USP analyses
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const uspPropositions = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section === 'usp_propositions')[0]
      : undefined;
    const uspAcidTest = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section === 'usp_acid_test')[0]
      : undefined;
    
    let uspContext = '';
    if (uspPropositions || uspAcidTest) {
      if (uspPropositions) {
        uspContext += `\n\nUSP PROPOSITIONS:\n${JSON.stringify(uspPropositions.analysis_data, null, 2)}`;
      }
      if (uspAcidTest) {
        uspContext += `\n\nUSP ACID TEST:\n${JSON.stringify(uspAcidTest.analysis_data, null, 2)}`;
      }
    }

    return `Analyze what makes this business truly unique and develop differentiation strategy:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

USP CONTEXT:${uspContext}

TASK: Identify and analyze the unique factors that differentiate this business.

Focus on:
1. UNIQUE FACTORS: Specific elements that make this business different
2. DIFFERENTIATION STRATEGY: How to leverage uniqueness for competitive advantage
3. COMPETITIVE ADVANTAGES: Sustainable advantages derived from unique factors
4. MARKET POSITIONING: How uniqueness translates to market position

For each unique factor, analyze:
- What makes it unique
- How it creates customer value
- Impact on competitive positioning
- Sustainability and defensibility

Develop a clear differentiation strategy that:
- Leverages core unique factors
- Creates sustainable competitive advantages
- Resonates with target customers
- Is difficult for competitors to replicate

Provide specific recommendations for communicating and leveraging uniqueness in marketing, sales, and product development.`;
  }

  private async buildExamplesPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get USP analyses
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const uspPropositions = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section === 'usp_propositions')[0]
      : undefined;
    const uspAcidTest = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section === 'usp_acid_test')[0]
      : undefined;
    const uspUniqueness = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section === 'usp_uniqueness')[0]
      : undefined;
    
    let uspContext = '';
    if (uspPropositions || uspAcidTest || uspUniqueness) {
      if (uspPropositions) {
        uspContext += `\n\nUSP PROPOSITIONS:\n${JSON.stringify(uspPropositions.analysis_data, null, 2)}`;
      }
      if (uspAcidTest) {
        uspContext += `\n\nUSP ACID TEST:\n${JSON.stringify(uspAcidTest.analysis_data, null, 2)}`;
      }
      if (uspUniqueness) {
        uspContext += `\n\nUSP UNIQUENESS:\n${JSON.stringify(uspUniqueness.analysis_data, null, 2)}`;
      }
    }

    return `Analyze USP best practices and develop compelling positioning examples:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

USP CONTEXT:${uspContext}

TASK: Research and analyze USP examples to inform positioning strategy.

Use web research to find:
1. GOOD USP EXAMPLES: Successful companies with compelling USPs
2. BAD USP EXAMPLES: Common USP mistakes and failures
3. PROJECT USP: Develop optimized USP for this specific project

For good examples, analyze:
- Company name and their USP statement
- Why the USP works effectively
- Key lessons and principles
- Relevance to this project

For bad examples, identify:
- Generic or weak USP statements
- Why they fail to differentiate
- How they could be improved
- Common mistakes to avoid

Develop a compelling USP for this project that:
- Applies lessons from successful examples
- Avoids common mistakes
- Is specific to this business and market
- Clearly communicates unique value

Research current market examples, successful positioning strategies, and industry best practices to inform recommendations.`;
  }

  private async buildCompetitiveRankingPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get strategic context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const strategicData = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('strategic_'))
      : [];
    
    let strategicContext = '';
    if (Array.isArray(strategicData)) {
      strategicData.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          strategicContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Conduct comprehensive competitive ranking and USP analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

STRATEGIC CONTEXT:${strategicContext}

TASK: Create a competitive ranking matrix and identify positioning opportunities.

Use web research to identify and analyze key competitors, then evaluate them across multiple criteria:

EVALUATION CRITERIA (1-10 scale):
1. INNOVATION: Product innovation and technological advancement
2. MARKET POSITION: Market share and brand recognition
3. CUSTOMER SATISFACTION: Customer loyalty and satisfaction ratings
4. PRICING: Pricing competitiveness and value proposition
5. BRAND STRENGTH: Brand awareness and reputation
6. OVERALL SCORE: Weighted average of all criteria

For each competitor, provide:
- Company name and market position (Leader/Challenger/Follower/Niche)
- Scores across all criteria with justification
- Key strengths and competitive advantages
- Notable weaknesses and vulnerabilities

Include this project as one of the competitors to show positioning opportunity.

Analyze:
- Competitive landscape insights
- Market positioning opportunities
- Gaps in the market that can be exploited
- Strategic recommendations for competitive advantage

Research current competitors, their market positioning, customer reviews, and industry analysis to provide accurate, data-driven rankings.`;
  }

  private async buildIntroductionPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all USP analyses
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const uspAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('usp_'))
      : [];

    let allAnalysesContext = '';
    if (Array.isArray(uspAnalyses)) {
      uspAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create a comprehensive introduction for the Unique Selling Points analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

ALL USP ANALYSES COMPLETED:${allAnalysesContext}

TASK: Create an executive summary that synthesizes all USP analyses.

Include:
1. EXECUTIVE SUMMARY: Overview of the USP analysis and positioning strategy
2. KEY FINDINGS: Most important insights from all USP analyses
3. COMPETITIVE POSITIONING: How this business positions against competitors
4. MAIN DIFFERENTIATORS: Core unique factors and competitive advantages
5. STRATEGIC RECOMMENDATIONS: Actionable recommendations for positioning and marketing
6. MARKETING IMPLICATIONS: How USP insights should guide marketing strategy

Provide a clear, compelling introduction that:
- Communicates the unique value proposition and positioning strategy
- Highlights key differentiators and competitive advantages
- Provides actionable recommendations for marketing and positioning
- Synthesizes all USP analyses into a coherent strategic framework

Focus on creating a positioning strategy that guides marketing, sales, and product development decisions while clearly communicating why customers should choose this solution.`;
  }

  private updateProgress(progress: USPGeneratorProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}