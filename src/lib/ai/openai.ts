// OpenAI API service that matches the Gemini interface
import OpenAI from 'openai';

export interface OpenAIConfig {
  systemInstruction?: string;
  temperature?: number;
  topP?: number;
  maxOutputTokens?: number;
  responseMimeType?: string;
  responseSchema?: any;
  tools?: any[];
  maxRetries?: number;
  retryDelayMs?: number;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export class OpenAIService {
  private config: OpenAIConfig;
  private chatHistory: ChatMessage[] = [];
  private openai: OpenAI;
  private logger: (level: string, message: string, data?: any) => void;

  constructor(config: OpenAIConfig = {}) {
    this.config = {
      temperature: 0.7,
      topP: 0.8,
      maxOutputTokens: 8192,
      maxRetries: 3,
      retryDelayMs: 1000,
      ...config
    };
    
    const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    if (!apiKey) {
      console.error('VITE_OPENAI_API_KEY is not defined in environment variables');
      throw new Error('VITE_OPENAI_API_KEY is required');
    }
    
    this.openai = new OpenAI({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true // Required for client-side usage
    });
    
    // Setup simple logger
    this.logger = (level: string, message: string, data?: any) => {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [OpenAIService] [${level}] ${message}`;
      
      if (level === 'error') {
        console.error(logMessage, data || '');
      } else if (level === 'warn') {
        console.warn(logMessage, data || '');
      } else {
        console.log(logMessage, data || '');
      }
    };
    
    this.logInfo('OpenAIService initialized');
  }
  
  private logInfo(message: string, data?: any) {
    this.logger('info', message, data);
  }
  
  private logWarn(message: string, data?: any) {
    this.logger('warn', message, data);
  }
  
  private logError(message: string, data?: any) {
    this.logger('error', message, data);
  }
  
  private logDebug(message: string, data?: any) {
    // Only log debug in development
    if (import.meta.env.DEV) {
      this.logger('debug', message, data);
    }
  }

  // Single message generation with retry logic
  async generateContent(prompt: string): Promise<any> {
    let retryCount = 0;
    const maxRetries = this.config.maxRetries || 3;
    
    while (true) {
      try {
        this.logInfo(`Generating content (attempt ${retryCount + 1}/${maxRetries + 1})`);
        
        // Prepare messages array
        const messages: any[] = [];
        
        // Add system instruction if provided
        if (this.config.systemInstruction) {
          messages.push({
            role: 'system',
            content: this.config.systemInstruction
          });
        }
        
        // Add user prompt
        messages.push({
          role: 'user',
          content: prompt
        });

        // Prepare request parameters
        const requestParams: any = {
          model: 'o3-mini',
          messages: messages,
          temperature: this.config.temperature,
          max_completion_tokens: this.config.maxOutputTokens,
          top_p: this.config.topP
        };

        // Handle JSON response format
        if (this.config.responseMimeType === 'application/json') {
          requestParams.response_format = { type: 'json_object' };
        }

        // Add tools if provided (function calling)
        if (this.config.tools && this.config.tools.length > 0) {
          // Convert Gemini-style tools to OpenAI format
          const openaiTools = this.convertGeminiToolsToOpenAI(this.config.tools);
          if (openaiTools.length > 0) {
            requestParams.tools = openaiTools;
            requestParams.tool_choice = 'auto';
          }
        }

        this.logDebug('API request', { 
          model: requestParams.model,
          messageCount: messages.length,
          toolsEnabled: !!requestParams.tools
        });

        const response = await this.openai.chat.completions.create(requestParams);

        const choice = response.choices[0];
        
        // Check for function calls
        if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
          this.logInfo('Function calls detected in response');
          return {
            type: 'function_call',
            functionCalls: choice.message.tool_calls.map((toolCall: any) => ({
              name: toolCall.function.name,
              args: JSON.parse(toolCall.function.arguments)
            })),
            text: choice.message.content || ''
          };
        }

        const text = choice.message.content || '';
        this.logInfo('Content generated successfully', { contentLength: text.length });
        
        return {
          type: 'text',
          text: text
        };
      } catch (error: any) {
        // Check if we should retry
        if (retryCount < maxRetries) {
          const delay = this.config.retryDelayMs! * Math.pow(2, retryCount); // Exponential backoff
          this.logWarn(`Error generating content (attempt ${retryCount + 1}/${maxRetries + 1}), retrying in ${delay}ms`, error);
          
          await new Promise(resolve => setTimeout(resolve, delay));
          retryCount++;
        } else {
          // Max retries reached, throw error
          this.logError('Max retries reached generating content', error);
          const errorMessage = error?.error?.message || error?.message || String(error);
          throw new Error(`Failed to generate content after ${maxRetries + 1} attempts: ${errorMessage}`);
        }
      }
    }
  }

  // Convert Gemini-style function declarations to OpenAI tools format
  private convertGeminiToolsToOpenAI(geminiTools: any[]): any[] {
    const openaiTools: any[] = [];
    
    for (const tool of geminiTools) {
      if (tool.functionDeclarations) {
        for (const func of tool.functionDeclarations) {
          openaiTools.push({
            type: 'function',
            function: {
              name: func.name,
              description: func.description,
              parameters: func.parameters
            }
          });
        }
      }
    }
    
    return openaiTools;
  }

  // Chat conversation with history
  async startChat(history: ChatMessage[] = []): Promise<void> {
    this.chatHistory = [...history];
  }

  async sendMessage(message: string): Promise<any> {
    try {
      // Add user message to history
      const userMessage: ChatMessage = {
        role: 'user',
        content: message
      };
      
      // Prepare messages array
      const messages: any[] = [];
      
      // Add system instruction if provided
      if (this.config.systemInstruction) {
        messages.push({
          role: 'system',
          content: this.config.systemInstruction
        });
      }
      
      // Add chat history
      messages.push(...this.chatHistory.map(msg => ({
        role: msg.role === 'assistant' ? 'assistant' : 'user',
        content: msg.content
      })));
      
      // Add current user message
      messages.push({
        role: 'user',
        content: message
      });

      const requestParams: any = {
        model: 'o3-mini',
        messages: messages,
        temperature: this.config.temperature,
        max_completion_tokens: this.config.maxOutputTokens,
        top_p: this.config.topP
      };

      // Handle JSON response format
      if (this.config.responseMimeType === 'application/json') {
        requestParams.response_format = { type: 'json_object' };
      }

      // Add tools if provided
      if (this.config.tools && this.config.tools.length > 0) {
        const openaiTools = this.convertGeminiToolsToOpenAI(this.config.tools);
        if (openaiTools.length > 0) {
          requestParams.tools = openaiTools;
          requestParams.tool_choice = 'auto';
        }
      }

      const response = await this.openai.chat.completions.create(requestParams);
      const choice = response.choices[0];
      const responseText = choice.message.content || '';
      
      // Add both user message and assistant response to history
      this.chatHistory.push(userMessage);
      this.chatHistory.push({
        role: 'assistant',
        content: responseText
      });

      // Check for function calls
      if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
        return {
          type: 'function_call',
          functionCalls: choice.message.tool_calls.map((toolCall: any) => ({
            name: toolCall.function.name,
            args: JSON.parse(toolCall.function.arguments)
          })),
          text: responseText
        };
      }

      return {
        type: 'text',
        text: responseText
      };
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message to OpenAI');
    }
  }

  // Get chat history
  async getHistory(): Promise<ChatMessage[]> {
    return [...this.chatHistory];
  }

  // Update configuration
  updateConfig(newConfig: Partial<OpenAIConfig>): void {
    this.config = { ...this.config, ...newConfig };
    // Reset chat history when config changes
    this.chatHistory = [];
  }
  
  // Get current configuration (avoiding API key exposure)
  getConfig(): Partial<OpenAIConfig> {
    // Return a copy without sensitive data
    const { ...safeConfig } = this.config;
    return safeConfig;
  }
}
