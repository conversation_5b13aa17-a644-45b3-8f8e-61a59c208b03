// Gemini API service using CURL/fetch instead of SDK
export interface GeminiConfig {
  systemInstruction?: string;
  temperature?: number;
  topP?: number;
  topK?: number;
  maxOutputTokens?: number;
  responseMimeType?: string;
  responseSchema?: any;
  tools?: any[];
  maxRetries?: number;
  retryDelayMs?: number;
}

export interface ChatMessage {
  role: 'user' | 'model';
  parts: Array<{ text: string }>;
}

export class GeminiService {
  private config: GeminiConfig;
  private chatHistory: ChatMessage[] = [];
  private apiKey: string;
  private logger: (level: string, message: string, data?: any) => void;

  constructor(config: GeminiConfig = {}) {
    this.config = {
      temperature: 0.7,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: 8192,
      maxRetries: 3,
      retryDelayMs: 1000,
      ...config
    };
    
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!this.apiKey) {
      console.error('VITE_GEMINI_API_KEY is not defined in environment variables');
      throw new Error('VITE_GEMINI_API_KEY is required');
    }
    
    // Setup simple logger
    this.logger = (level: string, message: string, data?: any) => {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [GeminiService] [${level}] ${message}`;
      
      if (level === 'error') {
        console.error(logMessage, data || '');
      } else if (level === 'warn') {
        console.warn(logMessage, data || '');
      } else {
        console.log(logMessage, data || '');
      }
    };
    
    this.logInfo('GeminiService initialized');
  }
  
  private logInfo(message: string, data?: any) {
    this.logger('info', message, data);
  }
  
  private logWarn(message: string, data?: any) {
    this.logger('warn', message, data);
  }
  
  private logError(message: string, data?: any) {
    this.logger('error', message, data);
  }
  
  private logDebug(message: string, data?: any) {
    // Only log debug in development
    if (import.meta.env.DEV) {
      this.logger('debug', message, data);
    }
  }

  // Single message generation with retry logic
  async generateContent(prompt: string): Promise<any> {
    let retryCount = 0;
    const maxRetries = this.config.maxRetries || 3;
    
    while (true) {
      try {
        this.logInfo(`Generating content (attempt ${retryCount + 1}/${maxRetries + 1})`);
        
        const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${this.apiKey}`;
        
        const requestBody: any = {
          contents: [
            {
              parts: [{ text: prompt }]
            }
          ]
        };

        // Add system instruction if provided
        if (this.config.systemInstruction) {
          requestBody.system_instruction = {
            parts: [{ text: this.config.systemInstruction }]
          };
        }

        // Add generation config
        if (this.config.temperature !== undefined || this.config.responseMimeType || this.config.responseSchema) {
          requestBody.generationConfig = {};
          
          if (this.config.temperature !== undefined) {
            requestBody.generationConfig.temperature = this.config.temperature;
          }
          if (this.config.topP !== undefined) {
            requestBody.generationConfig.topP = this.config.topP;
          }
          if (this.config.topK !== undefined) {
            requestBody.generationConfig.topK = this.config.topK;
          }
          if (this.config.maxOutputTokens !== undefined) {
            requestBody.generationConfig.maxOutputTokens = this.config.maxOutputTokens;
          }
          if (this.config.responseMimeType) {
            requestBody.generationConfig.responseMimeType = this.config.responseMimeType;
          }
          if (this.config.responseSchema) {
            requestBody.generationConfig.responseSchema = this.config.responseSchema;
          }
        }

        // Add tools if provided
        if (this.config.tools && this.config.tools.length > 0) {
          requestBody.tools = this.config.tools;
        }

        // Log request details at debug level (without API key)
        this.logDebug('API request', { 
          url: url.split('?')[0],
          configUsed: { 
            ...this.config,
            tools: this.config.tools ? 'Enabled' : 'Disabled' 
          }
        });

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          // Handle rate limiting specially (429)
          if (response.status === 429) {
            const retryAfter = response.headers.get('Retry-After');
            const retryMs = retryAfter ? parseInt(retryAfter) * 1000 : this.config.retryDelayMs;
            
            if (retryCount < maxRetries) {
              this.logWarn(`Rate limit hit, retrying in ${retryMs}ms`);
              await new Promise(resolve => setTimeout(resolve, retryMs));
              retryCount++;
              continue;
            }
          }
          
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.error) {
          throw new Error(`API error: ${data.error.message}`);
        }

        // Check for function calls
        const candidate = data.candidates?.[0];
        if (candidate?.content?.parts) {
          const functionCalls = candidate.content.parts.filter((part: any) => part.functionCall);
          if (functionCalls.length > 0) {
            this.logInfo('Function calls detected in response');
            return {
              type: 'function_call',
              functionCalls: functionCalls.map((part: any) => ({
                name: part.functionCall.name,
                args: part.functionCall.args
              })),
              text: candidate.content.parts.find((part: any) => part.text)?.text || ''
            };
          }
        }

        const text = candidate?.content?.parts?.[0]?.text || '';
        this.logInfo('Content generated successfully', { contentLength: text.length });
        
        return {
          type: 'text',
          text: text
        };
      } catch (error) {
        // Check if we should retry
        if (retryCount < maxRetries) {
          const delay = this.config.retryDelayMs * Math.pow(2, retryCount); // Exponential backoff
          this.logWarn(`Error generating content (attempt ${retryCount + 1}/${maxRetries + 1}), retrying in ${delay}ms`, error);
          
          await new Promise(resolve => setTimeout(resolve, delay));
          retryCount++;
        } else {
          // Max retries reached, throw error
          this.logError('Max retries reached generating content', error);
          throw new Error(`Failed to generate content after ${maxRetries + 1} attempts: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    }
  }

  // Chat conversation with history
  async startChat(history: ChatMessage[] = []): Promise<void> {
    this.chatHistory = [...history];
  }

  async sendMessage(message: string): Promise<any> {
    try {
      const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${this.apiKey}`;
      
      // Add user message to history
      const userMessage: ChatMessage = {
        role: 'user',
        parts: [{ text: message }]
      };
      
      const contents = [...this.chatHistory, userMessage];

      const requestBody: any = {
        contents: contents
      };

      // Add system instruction if provided
      if (this.config.systemInstruction) {
        requestBody.system_instruction = {
          parts: [{ text: this.config.systemInstruction }]
        };
      }

      // Add generation config
      if (this.config.temperature !== undefined || this.config.responseMimeType || this.config.responseSchema) {
        requestBody.generationConfig = {};
        
        if (this.config.temperature !== undefined) {
          requestBody.generationConfig.temperature = this.config.temperature;
        }
        if (this.config.responseMimeType) {
          requestBody.generationConfig.responseMimeType = this.config.responseMimeType;
        }
        if (this.config.responseSchema) {
          requestBody.generationConfig.responseSchema = this.config.responseSchema;
        }
      }

      // Add tools if provided
      if (this.config.tools && this.config.tools.length > 0) {
        requestBody.tools = this.config.tools;
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(`Gemini API error: ${data.error.message}`);
      }

      const candidate = data.candidates?.[0];
      const responseText = candidate?.content?.parts?.[0]?.text || '';
      
      // Add both user message and model response to history
      this.chatHistory.push(userMessage);
      this.chatHistory.push({
        role: 'model',
        parts: [{ text: responseText }]
      });

      // Check for function calls
      if (candidate?.content?.parts) {
        const functionCalls = candidate.content.parts.filter((part: any) => part.functionCall);
        if (functionCalls.length > 0) {
          return {
            type: 'function_call',
            functionCalls: functionCalls.map((part: any) => ({
              name: part.functionCall.name,
              args: part.functionCall.args
            })),
            text: responseText
          };
        }
      }

      return {
        type: 'text',
        text: responseText
      };
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message to Gemini');
    }
  }

  // Get chat history
  async getHistory(): Promise<ChatMessage[]> {
    return [...this.chatHistory];
  }

  // Update configuration
  updateConfig(newConfig: Partial<GeminiConfig>): void {
    this.config = { ...this.config, ...newConfig };
    // Reset chat history when config changes
    this.chatHistory = [];
  }
  
  // Get current configuration (avoiding API key exposure)
  getConfig(): Partial<GeminiConfig> {
    // Return a copy without the API key
    const { apiKey, ...safeConfig } = this.config as any;
    return safeConfig;
  }
}