import { AIService } from './aiService';
import { DatabaseService } from '../supabase';
import { responseSchemas } from './systemPrompts';

export interface CustomerPersonasProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: string[];
  isGenerating: boolean;
  error?: string;
}

export class CustomerPersonasService {
  private researchId: string;
  private projectData: any;
  private onProgress?: (progress: CustomerPersonasProgress) => void;

  constructor(researchId: string, projectData: any, onProgress?: (progress: CustomerPersonasProgress) => void) {
    this.researchId = researchId;
    this.projectData = projectData;
    this.onProgress = onProgress;
  }

  async generateCustomerPersonas(): Promise<void> {
    const steps = ['personas'];
    const completedSteps: string[] = [];

    try {
      // Update progress
      this.updateProgress({
        currentStep: 'personas',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: true
      });

      // Generate customer personas
      await this.generateAnalysis('personas');
      
      // Mark step as completed
      completedSteps.push('personas');
      
      // Final progress update
      this.updateProgress({
        currentStep: 'completed',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false
      });

    } catch (error) {
      this.updateProgress({
        currentStep: 'error',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async generateAnalysis(analysisType: string): Promise<any> {
    if (analysisType !== 'personas') {
      throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    const aiService = new AIService({
      section: 'customer_personas',
      useTools: false, // No web research - use existing data
      responseFormat: 'json',
      schema: responseSchemas.customerPersonas,
      temperature: 0.8
    });

    const prompt = await this.buildPersonasPrompt();

    // Generate analysis
    const response = await aiService.query(prompt);
    
    // Parse response
    let analysisData;
    if (response.type === 'text') {
      try {
        analysisData = JSON.parse(response.text);
      } catch (error) {
        throw new Error(`Failed to parse personas analysis response`);
      }
    } else {
      analysisData = JSON.parse(response.text);
    }

    // Save to database
    await DatabaseService.saveResearchAnalysis(this.researchId, 'customer_personas', analysisData);

    return analysisData;
  }

  private async buildPersonasPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all previous analyses for context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    
    // Organize analyses by type
    const strategicAnalyses = Array.isArray(analyses) 
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('strategic_'))
      : [];
    const mvpAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('mvp_'))
      : [];
    const uspAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('usp_'))
      : [];

    let allAnalysesContext = '';
    
    if (Array.isArray(strategicAnalyses) && strategicAnalyses.length > 0) {
      allAnalysesContext += '\n\nSTRATEGIC FRAMEWORK ANALYSES:';
      strategicAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    if (Array.isArray(mvpAnalyses) && mvpAnalyses.length > 0) {
      allAnalysesContext += '\n\nMVP ANALYSES:';
      mvpAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    if (Array.isArray(uspAnalyses) && uspAnalyses.length > 0) {
      allAnalysesContext += '\n\nUSP ANALYSES:';
      uspAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create detailed customer personas based on comprehensive business analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

COMPREHENSIVE BUSINESS ANALYSIS:${allAnalysesContext}

TASK: Create 5-8 detailed customer personas based on all the research and analysis conducted.

REQUIREMENTS:
- Create between 5-8 distinct customer personas
- Base personas on insights from Strategic Framework, MVP, and USP analyses
- Each persona should represent a different customer segment or use case
- Make personas realistic and specific, not generic
- Include comprehensive details for each persona
- Ensure personas are actionable for marketing and product development

For each persona, provide:

1. BASIC INFORMATION:
   - Unique ID (1-8)
   - Full name (realistic, professional)
   - Job role/title
   - Company name and type
   - Age (realistic for the role)
   - Location (specific city/region)

2. BACKGROUND:
   - Professional background and experience
   - Current role responsibilities
   - Industry context and company size

3. CHALLENGES (4-6 specific challenges):
   - Current pain points and frustrations
   - Obstacles they face in their role
   - Problems your solution could address

4. GOALS (4-6 specific goals):
   - Professional objectives and aspirations
   - What they want to achieve
   - Success metrics they care about

5. OBJECTIONS (4-6 potential objections):
   - Concerns about adopting new solutions
   - Budget, security, or implementation concerns
   - Skepticism or resistance factors

6. WHAT YOU CAN OFFER (4-6 value propositions):
   - Specific benefits for this persona
   - How your solution addresses their challenges
   - Value propositions that resonate with them

7. IDENTIFIERS (4-6 ways to find them):
   - Where they spend time online/offline
   - Professional communities they belong to
   - How to reach and engage with them

8. DEMOGRAPHICS:
   - Income range (realistic for role/location)
   - Education level and background
   - Years of experience in their field
   - Industry or sector focus

9. REAL QUOTES (3-4 authentic quotes):
   - Things they would actually say
   - Reflect their challenges and mindset
   - Sound natural and realistic

PERSONA INSIGHTS:
After creating all personas, provide:
- Target Segments: Overview of the different customer segments represented
- Common Themes: Shared challenges and goals across personas
- Value Differentiation: How value propositions differ by persona
- Marketing Implications: Key insights for marketing and customer acquisition

IMPORTANT GUIDELINES:
- Base personas on the business analysis data provided
- Make each persona distinct and realistic
- Avoid generic or stereotypical personas
- Ensure personas reflect the target market for this specific business
- Include specific details that make personas actionable
- Consider different company sizes, roles, and use cases
- Align personas with the USPs and value propositions identified

Create personas that marketing, sales, and product teams can use to make informed decisions about messaging, features, and customer acquisition strategies.`;
  }

  private updateProgress(progress: CustomerPersonasProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}