import { OpenAIService } from './openai';
import { functionDeclarations, executeFunctionCall } from './functions';
import { getSystemPrompt, responseSchemas } from './systemPrompts';

export interface AIServiceConfig {
  section?: string;
  useTools?: boolean;
  responseFormat?: 'text' | 'json';
  schema?: any;
  temperature?: number;
  maxRetries?: number;
  retryDelayMs?: number;
}

export class AIService {
  private openaiService: OpenAIService;
  private currentSection: string = 'general';
  private logger: (level: string, message: string, data?: any) => void;

  constructor(config: AIServiceConfig = {}) {
    const {
      section = 'general',
      useTools = true,
      responseFormat = 'text',
      schema,
      temperature = 0.7,
      maxRetries = 3,
      retryDelayMs = 1000
    } = config;

    this.currentSection = section;

    // Setup simple logger
    this.logger = (level: string, message: string, data?: any) => {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [AIService:${this.currentSection}] [${level}] ${message}`;
      
      if (level === 'error') {
        console.error(logMessage, data || '');
      } else if (level === 'warn') {
        console.warn(logMessage, data || '');
      } else {
        console.log(logMessage, data || '');
      }
    };

    // Configure OpenAI with appropriate settings
    const openaiConfig = {
      systemInstruction: getSystemPrompt(section),
      temperature,
      responseMimeType: responseFormat === 'json' ? 'application/json' : undefined,
      responseSchema: schema,
      tools: useTools ? [{
        functionDeclarations: Object.values(functionDeclarations)
      }] : undefined,
      maxRetries,
      retryDelayMs
    };

    this.openaiService = new OpenAIService(openaiConfig);
    this.logInfo(`Initialized for section: ${section}`);
  }
  
  private logInfo(message: string, data?: any) {
    this.logger('info', message, data);
  }
  
  private logWarn(message: string, data?: any) {
    this.logger('warn', message, data);
  }
  
  private logError(message: string, data?: any) {
    this.logger('error', message, data);
  }
  
  private logDebug(message: string, data?: any) {
    // Only log debug in development
    if (import.meta.env.DEV) {
      this.logger('debug', message, data);
    }
  }

  // Single query with potential function calls
  async query(prompt: string): Promise<any> {
    this.logInfo('Processing query', { promptLength: prompt.length });
    
    try {
      let response = await this.openaiService.generateContent(prompt);
      
      // Handle function calls
      if (response.type === 'function_call') {
        this.logInfo('Function calls detected, executing functions', { 
          functionCount: response.functionCalls.length 
        });
        
        const results = await this.handleFunctionCalls(response.functionCalls);
        
        // Send function results back to OpenAI for final response
        const functionResultsPrompt = this.formatFunctionResults(results);
        this.logInfo('Sending function results back to model');

        response = await this.openaiService.generateContent(
          `${prompt}\n\nFunction call results:\n${functionResultsPrompt}\n\nPlease provide your final analysis based on this research data.`
        );
      }
      
      // Validate JSON response if expected
      if (response.type === 'text' && this.openaiService.getConfig().responseMimeType === 'application/json') {
        try {
          // Attempt to parse as JSON to validate
          const parsed = JSON.parse(response.text);
          this.logDebug('Successfully validated JSON response', { 
            keys: Object.keys(parsed)
          });
        } catch (parseError) {
          this.logError('Failed to parse JSON response', { error: parseError });
          throw new Error(`Invalid JSON response: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
        }
      }
      
      this.logInfo('Query completed successfully');
      return response;
    } catch (error) {
      this.logError('Error in AI query', error);
      throw new Error(`Failed to process AI query: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Start a conversation
  async startConversation(initialMessage?: string): Promise<any> {
    await this.openaiService.startChat();
    
    if (initialMessage) {
      return await this.sendMessage(initialMessage);
    }
    
    return null;
  }

  // Send message in conversation
  async sendMessage(message: string): Promise<any> {
    try {
      let response = await this.openaiService.sendMessage(message);
      
      // Handle function calls in conversation
      if (response.type === 'function_call') {
        const results = await this.handleFunctionCalls(response.functionCalls);
        
        // Send function results back to continue conversation
        const functionResultsPrompt = this.formatFunctionResults(results);
        response = await this.openaiService.sendMessage(
          `Function call results:\n${functionResultsPrompt}\n\nPlease continue with your analysis based on this research data.`
        );
      }
      
      return response;
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message');
    }
  }

  // Handle multiple function calls with more robust error handling
  private async handleFunctionCalls(functionCalls: any[]): Promise<any[]> {
    const results = [];
    
    for (const functionCall of functionCalls) {
      try {
        this.logInfo(`Executing function: ${functionCall.name}`, { 
          args: functionCall.args 
        });
        
        const result = await executeFunctionCall(functionCall);
        this.logInfo(`Function ${functionCall.name} executed successfully`);
        
        results.push({
          function: functionCall.name,
          args: functionCall.args,
          result
        });
      } catch (error) {
        this.logError(`Function ${functionCall.name} failed`, error);
        
        results.push({
          function: functionCall.name,
          args: functionCall.args,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    return results;
  }

  // Format function results for Gemini
  private formatFunctionResults(results: any[]): string {
    return results.map(result => {
      if (result.error) {
        return `Function ${result.function} failed: ${result.error}`;
      }
      
      if (result.function === 'deep_research') {
        return `Research Results for "${result.args.query}":
Analysis: ${result.result.data.finalAnalysis}
Sources: ${result.result.data.sources.length} sources analyzed
Key findings from ${result.result.data.metadata.totalUrls} URLs`;
      }
      
      if (result.function === 'scrape_url') {
        return `URL Content from ${result.args.url}:
${result.result.data.markdown || result.result.data.content || 'Content extracted'}`;
      }
      
      return `Function ${result.function} completed successfully`;
    }).join('\n\n');
  }

  // Switch to different section
  switchSection(section: string, config: Partial<AIServiceConfig> = {}): void {
    this.currentSection = section;
    
    const openaiConfig = {
      systemInstruction: getSystemPrompt(section),
      temperature: config.temperature || 0.7,
      responseMimeType: config.responseFormat === 'json' ? 'application/json' : undefined,
      responseSchema: config.schema,
      tools: config.useTools !== false ? [{
        functionDeclarations: Object.values(functionDeclarations)
      }] : undefined,
      maxRetries: config.maxRetries || 3,
      retryDelayMs: config.retryDelayMs || 1000
    };

    this.openaiService.updateConfig(openaiConfig);
    this.logInfo(`Switched to section: ${section}`);
  }

  // Get conversation history
  async getHistory(): Promise<any[]> {
    return await this.openaiService.getHistory();
  }

  // Specialized methods for different analysis types
  async analyzeMarketSize(businessIdea: string): Promise<any> {
    this.switchSection('dashboard', {
      responseFormat: 'json',
      schema: responseSchemas.marketSizing
    });

    const prompt = `Analyze the market size and opportunity for this business idea: "${businessIdea}"

Please provide:
1. TAM (Total Addressable Market)
2. SAM (Serviceable Addressable Market) 
3. SOM (Serviceable Obtainable Market)
4. Viability score (1-10)
5. Key market insights
6. Sources used for analysis

Use web research to get current market data and industry reports.`;

    return await this.query(prompt);
  }

  async generateSWOT(businessIdea: string, industry: string): Promise<any> {
    this.switchSection('strategic', {
      responseFormat: 'json',
      schema: responseSchemas.swotAnalysis
    });

    const prompt = `Conduct a comprehensive SWOT analysis for: "${businessIdea}" in the ${industry} industry.

Research current industry trends, competitive landscape, and market conditions to provide accurate analysis.

Include:
- Strengths: Internal positive factors
- Weaknesses: Internal challenges
- Opportunities: External positive factors
- Threats: External challenges
- Strategic recommendations based on the analysis`;

    return await this.query(prompt);
  }

  async createCustomerPersona(businessIdea: string, targetMarket: string): Promise<any> {
    this.switchSection('personas', {
      responseFormat: 'json',
      schema: responseSchemas.customerPersona
    });

    const prompt = `Create a detailed customer persona for: "${businessIdea}" targeting: "${targetMarket}"

Research current market demographics, customer behavior patterns, and preferences in this market segment.

Provide a comprehensive persona including demographics, psychographics, pain points, goals, and preferred communication channels.`;

    return await this.query(prompt);
  }
}

// Export singleton instance
export const aiService = new AIService();