import { AIService } from './aiService';
import { DatabaseService } from '../supabase';
import { responseSchemas } from './systemPrompts';

export interface CompetitiveAnalysisProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: string[];
  isGenerating: boolean;
  error?: string;
}

export class CompetitiveAnalysisService {
  private researchId: string;
  private projectData: any;
  private onProgress?: (progress: CompetitiveAnalysisProgress) => void;

  constructor(researchId: string, projectData: any, onProgress?: (progress: CompetitiveAnalysisProgress) => void) {
    this.researchId = researchId;
    this.projectData = projectData;
    this.onProgress = onProgress;
  }

  async generateCompleteCompetitiveAnalysis(): Promise<void> {
    const steps = [
      'resources_capabilities',
      'real_world_insights',
      'competitive_benchmark',
      'introduction'
    ];

    const completedSteps: string[] = [];

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: true
        });

        // Generate analysis for current step
        await this.generateAnalysis(step);
        
        // Mark step as completed
        completedSteps.push(step);
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: false
        });
      }

      // Final progress update
      this.updateProgress({
        currentStep: 'completed',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false
      });

    } catch (error) {
      this.updateProgress({
        currentStep: 'error',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async generateAnalysis(analysisType: string): Promise<any> {
    let aiService: AIService;
    let prompt: string;
    let schema: any;

    // Configure AI service based on analysis type
    switch (analysisType) {
      case 'resources_capabilities':
        aiService = new AIService({
          section: 'competitive_resources_capabilities',
          useTools: false, // No web research - use existing data
          responseFormat: 'json',
          schema: responseSchemas.competitiveResourcesCapabilities,
          temperature: 0.7
        });
        prompt = await this.buildResourcesCapabilitiesPrompt();
        schema = responseSchemas.competitiveResourcesCapabilities;
        break;

      case 'real_world_insights':
        aiService = new AIService({
          section: 'competitive_real_world_insights',
          useTools: false, // No web research - use existing data
          responseFormat: 'json',
          schema: responseSchemas.competitiveRealWorldInsights,
          temperature: 0.7
        });
        prompt = await this.buildRealWorldInsightsPrompt();
        schema = responseSchemas.competitiveRealWorldInsights;
        break;

      case 'competitive_benchmark':
        aiService = new AIService({
          section: 'competitive_benchmark',
          useTools: false, // No web research - use existing data
          responseFormat: 'json',
          schema: responseSchemas.competitiveBenchmark,
          temperature: 0.7
        });
        prompt = await this.buildCompetitiveBenchmarkPrompt();
        schema = responseSchemas.competitiveBenchmark;
        break;

      case 'introduction':
        aiService = new AIService({
          section: 'competitive_introduction',
          useTools: false, // No web research - synthesis only
          responseFormat: 'json',
          schema: responseSchemas.competitiveIntroduction,
          temperature: 0.7
        });
        prompt = await this.buildIntroductionPrompt();
        schema = responseSchemas.competitiveIntroduction;
        break;

      default:
        throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    // Generate analysis
    const response = await aiService.query(prompt);
    
    // Parse response
    let analysisData;
    if (response.type === 'text') {
      try {
        analysisData = JSON.parse(response.text);
      } catch (error) {
        throw new Error(`Failed to parse ${analysisType} analysis response`);
      }
    } else {
      analysisData = JSON.parse(response.text);
    }

    // Save to database
    await DatabaseService.saveResearchAnalysis(this.researchId, `competitive_${analysisType}`, analysisData);

    return analysisData;
  }

  private async buildResourcesCapabilitiesPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all previous analyses for comprehensive context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId);
    const strategicAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('strategic_'));
    const uspAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('usp_'));
    const mvpAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('mvp_'));
    const financialAnalyses = analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('finances_'));
    
    let allAnalysesContext = '';
    
    if (Array.isArray(strategicAnalyses) && strategicAnalyses.length > 0) {
      allAnalysesContext += '\n\nSTRATEGIC FRAMEWORK ANALYSES:';
      strategicAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    if (Array.isArray(uspAnalyses) && uspAnalyses.length > 0) {
      allAnalysesContext += '\n\nUSP ANALYSES:';
      uspAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    if (Array.isArray(mvpAnalyses) && mvpAnalyses.length > 0) {
      allAnalysesContext += '\n\nMVP ANALYSES:';
      mvpAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    if (Array.isArray(financialAnalyses) && financialAnalyses.length > 0) {
      allAnalysesContext += '\n\nFINANCIAL ANALYSES:';
      financialAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Analyze resources and capabilities using VRIO framework based on comprehensive business research:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

COMPREHENSIVE BUSINESS ANALYSIS:${allAnalysesContext}

TASK: Conduct VRIO (Value, Rarity, Imitability, Organization) analysis of key resources and capabilities.

Based on all the business research conducted, analyze the key resources and capabilities using the VRIO framework:

For each resource/capability, evaluate:
1. VALUE: Does this resource/capability enable the firm to exploit opportunities or neutralize threats?
2. RARITY: Is this resource/capability currently controlled by only a small number of competing firms?
3. IMITABILITY: Do firms without this resource/capability face a cost disadvantage in obtaining it?
4. ORGANIZATION: Is the firm organized to capture the value of this resource/capability?

Identify and analyze 3-5 key resources/capabilities such as:
- Proprietary technology or intellectual property
- Unique business model or approach
- Team expertise and capabilities
- Strategic partnerships or relationships
- Brand and market position
- Financial resources and funding
- Operational capabilities and processes

For each resource/capability, provide:
- Resource/capability name and description
- VRIO analysis with ratings (High/Medium/Low for each dimension)
- Competitive outcome (Sustained Competitive Advantage/Temporary Competitive Advantage/Competitive Parity/Competitive Disadvantage)
- Strategic implications and recommendations
- Sustainability assessment and threats
- Enhancement opportunities

Base the analysis on:
- Strategic framework insights about competitive positioning
- USP analysis and differentiation factors
- MVP capabilities and technical advantages
- Financial resources and investment capacity
- Market positioning and competitive advantages identified

Provide realistic assessments based on the business analysis data, avoiding overestimation of capabilities while identifying genuine competitive advantages and areas for improvement.`;
  }

  private async buildRealWorldInsightsPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all previous analyses for comprehensive context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId);
    const allAnalyses = analyses.filter(a => 
      a && typeof a === 'object' && (
        a.section.startsWith('strategic_') || 
        a.section.startsWith('usp_') ||
        a.section.startsWith('finances_') ||
        a.section.startsWith('gtm_') ||
        a.section === 'customer_personas'
      )
    );
    
    let allAnalysesContext = '';
    allAnalyses.forEach(analysis => {
      allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
    });

    return `Create real-world competitive insights including positioning map and strategic analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

COMPREHENSIVE BUSINESS ANALYSIS:${allAnalysesContext}

TASK: Develop real-world competitive insights with positioning map, advantages, challenges, and response strategy.

Based on all business research conducted, create comprehensive competitive insights:

1. COMPETITIVE POSITIONING MAP:
   - Identify 2 key dimensions for competitive positioning (e.g., Price vs. Features, Enterprise Focus vs. AI Sophistication)
   - Position this business and 4-6 key competitors on the map
   - Provide coordinates (1-100 scale) for each competitor on both dimensions
   - Include market position classification (Leader/Challenger/Follower/Niche)

2. COMPETITIVE ADVANTAGES:
   - Identify 4-6 key competitive advantages based on business analysis
   - Explain how each advantage creates customer value
   - Assess sustainability and defensibility of each advantage
   - Connect advantages to business capabilities and resources

3. COMPETITIVE CHALLENGES:
   - Identify 4-6 key competitive challenges and threats
   - Assess impact and probability of each challenge
   - Analyze competitive responses and market dynamics
   - Consider resource constraints and market position

4. COMPETITIVE RESPONSE STRATEGY:
   - Develop strategic responses to competitive challenges
   - Leverage competitive advantages for market positioning
   - Create differentiation and positioning strategies
   - Plan competitive monitoring and response processes

For the positioning map, consider dimensions such as:
- Price vs. Quality/Features
- Enterprise Focus vs. Consumer Focus
- Innovation vs. Reliability
- Breadth vs. Depth of offering
- Technology sophistication vs. Ease of use

For competitive advantages, analyze:
- Unique technology or approach
- Superior customer experience
- Cost advantages or efficiency
- Market timing and first-mover benefits
- Strategic partnerships or ecosystem
- Brand strength and reputation

For competitive challenges, consider:
- Well-funded competitors with similar offerings
- Market saturation or commoditization
- Technology disruption or obsolescence
- Regulatory or compliance challenges
- Customer acquisition difficulties
- Resource or capability limitations

Base insights on:
- Strategic framework competitive analysis
- USP differentiation and positioning
- Financial capabilities and constraints
- Go-to-market strategy and execution
- Customer personas and market segments
- MVP capabilities and product positioning

Provide actionable insights that inform strategic decision-making, competitive positioning, and market strategy while being realistic about competitive dynamics and market position.`;
  }

  private async buildCompetitiveBenchmarkPrompt(): string {
    const { research, questions } = this.projectData;
    const answeredQuestions = questions.filter((q: any) => q.answer).map((q: any) => `Q: ${q.question}\nA: ${q.answer}`).join('\n\n');

    // Get all previous analyses for comprehensive context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId);
    const allAnalyses = analyses.filter(a => 
      a.section.startsWith('strategic_') || 
      a.section.startsWith('usp_') ||
      a.section.startsWith('mvp_') ||
      a.section.startsWith('finances_') ||
      a.section.startsWith('gtm_')
    );
    
    let allAnalysesContext = '';
    allAnalyses.forEach(analysis => {
      allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
    });

    return `Create comprehensive competitive benchmark analysis with detailed comparison matrix:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

COMPREHENSIVE BUSINESS ANALYSIS:${allAnalysesContext}

TASK: Develop detailed competitive benchmark with feature comparison and market positioning analysis.

Based on all business research conducted, create comprehensive competitive benchmark:

1. COMPETITIVE BENCHMARK MATRIX:
   - Include this business plus 4-6 key competitors
   - Evaluate across 6-8 key criteria relevant to the business
   - Use 1-5 star rating system for each criterion
   - Provide overall competitive assessment

2. BENCHMARK CRITERIA:
   Select criteria based on business analysis, such as:
   - Product Innovation/Technology
   - Market Position/Brand Strength
   - Customer Experience/Satisfaction
   - Pricing/Value Proposition
   - Enterprise Features/Capabilities
   - Team/Execution Capability
   - Financial Strength/Resources
   - Go-to-Market Effectiveness

3. COMPETITOR PROFILES:
   For each competitor, provide:
   - Company name and market position
   - Key strengths and competitive advantages
   - Notable weaknesses and vulnerabilities
   - Market share and customer base
   - Pricing strategy and business model
   - Recent developments and strategic moves

4. COMPETITIVE INSIGHTS:
   - Market leadership analysis
   - Competitive gaps and opportunities
   - Differentiation opportunities
   - Strategic recommendations
   - Market positioning implications

For each competitor in the benchmark:
- Provide realistic ratings (1-5 stars) based on market position and capabilities
- Include brief justification for ratings
- Consider both current performance and strategic potential
- Account for market perception and customer feedback

Base competitor selection and analysis on:
- Strategic framework competitive analysis
- USP competitive positioning insights
- MVP feature and capability comparisons
- Financial market research and competitive data
- Go-to-market competitive intelligence

Ensure benchmark is:
- Realistic and based on available market information
- Relevant to key success factors in the industry
- Actionable for strategic decision-making
- Balanced in assessment (not overly optimistic or pessimistic)
- Focused on criteria that matter to customers and market success

Provide strategic insights on:
- Where this business can compete effectively
- Areas requiring improvement or investment
- Competitive positioning opportunities
- Market entry and growth strategies
- Differentiation and value proposition refinement

The benchmark should inform strategic planning, product development, marketing positioning, and competitive strategy while providing realistic assessment of market position and opportunities.`;
  }

  private async buildIntroductionPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all competitive analyses
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const competitiveAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('competitive_'))
      : [];

    let allAnalysesContext = '';
    if (Array.isArray(competitiveAnalyses)) {
      competitiveAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create a comprehensive introduction for the Competitive Analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

ALL COMPETITIVE ANALYSES COMPLETED:${allAnalysesContext}

TASK: Create an executive summary that synthesizes all competitive analyses.

Include:
1. EXECUTIVE SUMMARY: Overview of the competitive landscape and positioning analysis
2. KEY FINDINGS: Most important competitive insights and market dynamics
3. COMPETITIVE POSITION: Current market position and competitive standing
4. STRATEGIC ADVANTAGES: Core competitive advantages and differentiation factors
5. MARKET CHALLENGES: Key competitive challenges and threats
6. STRATEGIC RECOMMENDATIONS: Actionable recommendations for competitive strategy
7. COMPETITIVE OUTLOOK: Future competitive dynamics and market evolution

Provide a clear, compelling introduction that:
- Communicates the competitive landscape and market dynamics
- Highlights key competitive advantages and positioning strengths
- Addresses competitive challenges and strategic responses
- Provides actionable recommendations for competitive strategy
- Sets realistic expectations for competitive performance

Synthesize insights from:
- Resources and capabilities analysis (VRIO framework)
- Real-world competitive insights and positioning
- Competitive benchmark and market comparison
- Strategic implications and recommendations

Focus on creating a competitive overview that guides strategic decision-making, market positioning, and competitive strategy while clearly communicating the business's competitive potential and market opportunities.

The introduction should help stakeholders understand:
- Where the business stands competitively
- What advantages can be leveraged
- What challenges need to be addressed
- How to compete effectively in the market
- Strategic priorities for competitive success`;
  }

  private updateProgress(progress: CompetitiveAnalysisProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}