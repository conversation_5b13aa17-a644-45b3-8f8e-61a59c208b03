import { FirecrawlService } from './firecrawl';

// Initialize Firecrawl service
const firecrawlService = new FirecrawlService();

// Function declarations for Gemini
export const functionDeclarations = {
  deepResearch: {
    name: 'deep_research',
    description: 'Performs deep web research on a specific topic, analyzing multiple sources and providing comprehensive insights. Use this when you need current market data, competitor information, industry trends, or any information that requires web research.',
    parameters: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'The research query or topic to investigate. Be specific and detailed for better results. Example: "AI development tools market size and growth trends 2024"'
        },
        maxDepth: {
          type: 'integer',
          description: 'Number of research iterations (1-5). Higher values provide more comprehensive results but take longer.',
          enum: [1, 2, 3, 4, 5]
        },
        maxUrls: {
          type: 'integer',
          description: 'Maximum number of URLs to analyze (5-20). More URLs provide broader coverage.',
          enum: [5, 8, 10, 15, 20]
        },
        timeLimit: {
          type: 'integer',
          description: 'Time limit in seconds (60-300). Longer times allow for more thorough research.',
          enum: [60, 120, 180, 240, 300]
        },
        includeDomains: {
          type: 'array',
          items: { type: 'string' },
          description: 'Specific domains to focus on (optional). Example: ["techcrunch.com", "venturebeat.com"]'
        }
      },
      required: ['query']
    }
  },

  scrapeUrl: {
    name: 'scrape_url',
    description: 'Scrapes content from a specific URL to get detailed information about a webpage. Use this when you have a specific URL that contains relevant information.',
    parameters: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'The URL to scrape. Must be a valid HTTP/HTTPS URL.'
        }
      },
      required: ['url']
    }
  }
};

// Function execution handlers
export const functionHandlers = {
  deep_research: async (args: any) => {
    try {
      const { query, maxDepth = 3, maxUrls = 8, timeLimit = 120, includeDomains } = args;
      
      const result = await firecrawlService.deepResearch(query, {
        maxDepth,
        maxUrls,
        timeLimit,
        includeDomains
      });
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  },

  scrape_url: async (args: any) => {
    try {
      const { url } = args;
      
      const result = await firecrawlService.scrapeUrl(url);
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
};

// Execute function calls from Gemini
export async function executeFunctionCall(functionCall: any): Promise<any> {
  const { name, args } = functionCall;
  
  if (functionHandlers[name as keyof typeof functionHandlers]) {
    return await functionHandlers[name as keyof typeof functionHandlers](args);
  }
  
  throw new Error(`Unknown function: ${name}`);
}