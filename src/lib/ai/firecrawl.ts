// Firecrawl API service using CURL/fetch instead of SDK

export interface DeepResearchParams {
  maxDepth?: number;
  timeLimit?: number;
  maxUrls?: number;
  includeDomains?: string[];
  excludeDomains?: string[];
  lang?: string;
}

export interface ResearchActivity {
  type: string;
  message: string;
  timestamp?: Date;
  status?: string;
  depth?: number;
}

export interface ResearchResult {
  finalAnalysis: string;
  sources: Array<{
    url: string;
    title: string;
    description: string;
    content?: string;
  }>;
  activities: ResearchActivity[];
  metadata: {
    totalUrls: number;
    totalTime: number;
    depth: number;
  };
}

export class FirecrawlService {
  private activities: ResearchActivity[] = [];
  private apiKey: string;

  constructor() {
    this.apiKey = import.meta.env.VITE_FIRECRAWL_API_KEY;
    if (!this.apiKey) {
      throw new Error('VITE_FIRECRAWL_API_KEY is required');
    }
  }

  // Deep research function
  async deepResearch(
    query: string, 
    params: DeepResearchParams = {},
    onActivity?: (activity: ResearchActivity) => void
  ): Promise<ResearchResult> {
    try {
      this.activities = [];
      
      const defaultParams = {
        maxDepth: 3,
        timeLimit: 120,
        maxUrls: 8,
        lang: 'es',
        ...params
      };

      const url = 'https://api.firecrawl.dev/v1/deep-research';
      
      const requestBody = {
        query,
        maxDepth: defaultParams.maxDepth,
        timeLimit: defaultParams.timeLimit,
        maxUrls: defaultParams.maxUrls,
        ...(defaultParams.includeDomains && { includeDomains: defaultParams.includeDomains }),
        ...(defaultParams.excludeDomains && { excludeDomains: defaultParams.excludeDomains })
      };

      // Start activity tracking
      const startActivity: ResearchActivity = {
        type: 'search',
        message: `Starting deep research for: "${query}"`,
        timestamp: new Date(),
        status: 'started',
        depth: 0
      };
      
      this.activities.push(startActivity);
      if (onActivity) onActivity(startActivity);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Firecrawl API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(`Firecrawl API error: ${data.error || 'Unknown error'}`);
      }

      // Process activities from response
      if (data.data?.activities) {
        data.data.activities.forEach((activity: any) => {
          const processedActivity: ResearchActivity = {
            type: activity.type || 'info',
            message: activity.message || '',
            timestamp: activity.timestamp ? new Date(activity.timestamp) : new Date(),
            status: activity.status,
            depth: activity.depth
          };
          
          this.activities.push(processedActivity);
          if (onActivity) onActivity(processedActivity);
        });
      }

      // Completion activity
      const completionActivity: ResearchActivity = {
        type: 'completed',
        message: `Research completed. Found ${data.data?.sources?.length || 0} sources.`,
        timestamp: new Date(),
        status: 'completed',
        depth: defaultParams.maxDepth
      };
      
      this.activities.push(completionActivity);
      if (onActivity) onActivity(completionActivity);

      return {
        finalAnalysis: data.data?.finalAnalysis || 'No analysis available',
        sources: data.data?.sources || [],
        activities: this.activities,
        metadata: {
          totalUrls: data.data?.sources?.length || 0,
          totalTime: defaultParams.timeLimit,
          depth: defaultParams.maxDepth
        }
      };
    } catch (error) {
      console.error('Error in deep research:', error);
      
      // Add error activity
      const errorActivity: ResearchActivity = {
        type: 'error',
        message: `Research failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        status: 'failed'
      };
      
      this.activities.push(errorActivity);
      if (onActivity) onActivity(errorActivity);
      
      // Return fallback result instead of throwing
      return {
        finalAnalysis: 'Unable to complete research due to API limitations. Please try again later.',
        sources: [],
        activities: this.activities,
        metadata: {
          totalUrls: 0,
          totalTime: 0,
          depth: 0
        }
      };
    }
  }

  // Simple web scraping for single URLs (fallback method)
  async scrapeUrl(url: string): Promise<any> {
    try {
      const scrapeUrl = 'https://api.firecrawl.dev/v1/scrape';
      
      const requestBody = {
        url,
        formats: ['markdown', 'html'],
        includeTags: ['title', 'meta', 'h1', 'h2', 'h3', 'p'],
        excludeTags: ['script', 'style', 'nav', 'footer']
      };

      const response = await fetch(scrapeUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Firecrawl scrape error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(`Firecrawl scrape error: ${data.error || 'Unknown error'}`);
      }

      return data.data;
    } catch (error) {
      console.error('Error scraping URL:', error);
      throw new Error(`Failed to scrape URL: ${url}`);
    }
  }

  // Batch URL crawling
  async crawlUrls(urls: string[]): Promise<any[]> {
    try {
      const results = await Promise.allSettled(
        urls.map(url => this.scrapeUrl(url))
      );
      
      return results
        .filter(result => result.status === 'fulfilled')
        .map(result => (result as PromiseFulfilledResult<any>).value);
    } catch (error) {
      console.error('Error crawling URLs:', error);
      throw new Error('Failed to crawl URLs');
    }
  }

  // Get recent activities
  getActivities(): ResearchActivity[] {
    return this.activities;
  }

  // Clear activities
  clearActivities(): void {
    this.activities = [];
  }
}