import { AIService } from './aiService';
import { DatabaseService } from '../supabase';
import { responseSchemas } from './systemPrompts';

export interface MVPGeneratorProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: string[];
  isGenerating: boolean;
  error?: string;
}

export class MVPGeneratorService {
  private researchId: string;
  private projectData: any;
  private onProgress?: (progress: MVPGeneratorProgress) => void;

  constructor(researchId: string, projectData: any, onProgress?: (progress: MVPGeneratorProgress) => void) {
    this.researchId = researchId;
    this.projectData = projectData;
    this.onProgress = onProgress;
  }

  async generateCompleteMVPFramework(): Promise<void> {
    const steps = [
      'core_features',
      'market_validation',
      'timeline_milestones',
      'marketing_strategy',
      'budget_considerations',
      'performance_metrics',
      'introduction'
    ];

    const completedSteps: string[] = [];

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: true
        });

        // Generate analysis for current step
        await this.generateAnalysis(step);
        
        // Mark step as completed
        completedSteps.push(step);
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: false
        });
      }

      // Final progress update
      this.updateProgress({
        currentStep: 'completed',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false
      });

    } catch (error) {
      this.updateProgress({
        currentStep: 'error',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async generateAnalysis(analysisType: string): Promise<any> {
    let aiService: AIService;
    let prompt: string;
    let schema: any;

    // Configure AI service based on analysis type
    switch (analysisType) {
      case 'core_features':
        aiService = new AIService({
          section: 'mvp_core_features',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.mvpCoreFeatures,
          temperature: 0.7
        });
        prompt = this.buildCoreFeaturesPrompt();
        schema = responseSchemas.mvpCoreFeatures;
        break;

      case 'market_validation':
        aiService = new AIService({
          section: 'mvp_market_validation',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.mvpMarketValidation,
          temperature: 0.7
        });
        prompt = this.buildMarketValidationPrompt();
        schema = responseSchemas.mvpMarketValidation;
        break;

      case 'timeline_milestones':
        aiService = new AIService({
          section: 'mvp_timeline_milestones',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.mvpTimelineMilestones,
          temperature: 0.7
        });
        prompt = this.buildTimelineMilestonesPrompt();
        schema = responseSchemas.mvpTimelineMilestones;
        break;

      case 'marketing_strategy':
        aiService = new AIService({
          section: 'mvp_marketing_strategy',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.mvpMarketingStrategy,
          temperature: 0.7
        });
        prompt = this.buildMarketingStrategyPrompt();
        schema = responseSchemas.mvpMarketingStrategy;
        break;

      case 'budget_considerations':
        aiService = new AIService({
          section: 'mvp_budget_considerations',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.mvpBudgetConsiderations,
          temperature: 0.7
        });
        prompt = this.buildBudgetConsiderationsPrompt();
        schema = responseSchemas.mvpBudgetConsiderations;
        break;

      case 'performance_metrics':
        aiService = new AIService({
          section: 'mvp_performance_metrics',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.mvpPerformanceMetrics,
          temperature: 0.7
        });
        prompt = this.buildPerformanceMetricsPrompt();
        schema = responseSchemas.mvpPerformanceMetrics;
        break;

      case 'introduction':
        aiService = new AIService({
          section: 'mvp_introduction',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.mvpIntroduction,
          temperature: 0.7
        });
        prompt = await this.buildIntroductionPrompt();
        schema = responseSchemas.mvpIntroduction;
        break;

      default:
        throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    // Generate analysis
    const response = await aiService.query(prompt);
    
    // Parse response
    let analysisData;
    if (response.type === 'text') {
      try {
        analysisData = JSON.parse(response.text);
      } catch (error) {
        throw new Error(`Failed to parse ${analysisType} analysis response`);
      }
    } else {
      analysisData = JSON.parse(response.text);
    }

    // Save to database
    await DatabaseService.saveResearchAnalysis(this.researchId, `mvp_${analysisType}`, analysisData);

    return analysisData;
  }

  private buildCoreFeaturesPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    return `Analyze and prioritize core features for the MVP of this business idea:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Identify and prioritize the core features needed for a successful MVP.

Focus on:
1. CORE FEATURES: Essential features that deliver the primary value proposition
2. FEATURE PRIORITIZATION: Use frameworks like MoSCoW (Must have, Should have, Could have, Won't have)
3. MVP SCOPE: Define the minimum viable scope that validates key assumptions
4. TECHNICAL CONSIDERATIONS: Assess technical complexity and implementation challenges

For each feature, provide:
- Feature name and description
- Priority level (High/Medium/Low)
- Development effort estimate
- Current status (Planning/In Progress/Completed/Backlog)
- User value and business impact
- Technical complexity assessment

Ensure the MVP scope is achievable within 3-6 months with a small team while providing meaningful value to early users.`;
  }

  private buildMarketValidationPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    return `Design a comprehensive market validation strategy for this business idea:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Create a market validation plan to test key business assumptions before full development.

Focus on:
1. VALIDATION METHODS: Specific techniques to validate market demand and product-market fit
2. KEY ASSUMPTIONS: Critical assumptions that need validation
3. SUCCESS CRITERIA: Metrics and thresholds that indicate validation success
4. RISK MITIGATION: Strategies to address potential validation failures
5. VALIDATION TIMELINE: Realistic timeline for validation activities

Design validation experiments that:
- Test core value propositions with target customers
- Validate pricing and willingness to pay
- Assess market size and demand
- Identify early adopters and customer segments
- Minimize resource investment while maximizing learning

Provide specific, actionable validation methods with clear success criteria and timelines.`;
  }

  private buildTimelineMilestonesPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    return `Create a detailed timeline and milestone plan for MVP development and launch:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Develop a realistic timeline with key milestones for MVP development and market entry.

Focus on:
1. TIMELINE PHASES: Break down development into logical phases with durations
2. KEY MILESTONES: Critical checkpoints that enable progress tracking
3. CRITICAL PATH: Dependencies and bottlenecks that could impact timeline
4. RISK FACTORS: Potential delays and mitigation strategies

Include phases for:
- Market validation and customer discovery
- MVP development and testing
- Beta testing and feedback incorporation
- Launch preparation and go-to-market execution
- Post-launch optimization and scaling

For each phase, specify:
- Duration and timeline
- Key deliverables and outcomes
- Resource requirements
- Dependencies and prerequisites
- Success criteria

Assume a small team (3-8 people) with limited resources but high execution capability.`;
  }

  private buildMarketingStrategyPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    return `Develop a comprehensive marketing strategy for MVP launch and early customer acquisition:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Create a cost-effective marketing strategy for early-stage customer acquisition.

Focus on:
1. MARKETING CHANNELS: Identify the most effective channels for reaching target customers
2. TARGET AUDIENCE: Define specific customer segments and personas
3. VALUE PROPOSITION: Articulate compelling value propositions for each segment
4. CONTENT STRATEGY: Plan content that educates, engages, and converts
5. BUDGET ALLOCATION: Distribute marketing budget across channels and activities

For each marketing channel, provide:
- Channel description and strategy
- Estimated reach and audience size
- Investment required and cost structure
- Expected ROI and conversion metrics
- Implementation timeline and priority

Focus on:
- Low-cost, high-impact marketing tactics
- Community building and word-of-mouth strategies
- Content marketing and thought leadership
- Early adopter engagement and referral programs
- Performance tracking and optimization

Ensure strategies are executable with limited budget (under $50K) while maximizing customer acquisition and brand awareness.`;
  }

  private buildBudgetConsiderationsPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    return `Create a comprehensive budget analysis for MVP development and initial operations:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Develop realistic budget estimates for all aspects of MVP development and launch.

Focus on:
1. DEVELOPMENT COSTS: Engineering, design, infrastructure, testing
2. MARKETING COSTS: Customer acquisition, content creation, advertising
3. OPERATING COSTS: Legal, office, administrative, contingency
4. TOTAL INVESTMENT: Overall funding requirements and runway analysis
5. FUNDING STRATEGY: Recommended funding approach and sources

Provide detailed breakdowns for:
- Engineering team costs (salaries, contractors, tools)
- Infrastructure and technology costs (hosting, software, services)
- Design and user experience costs
- Marketing and customer acquisition costs
- Legal and compliance costs
- Office and operational expenses
- Contingency and risk buffer

Include:
- 6-month and 12-month budget projections
- Cost optimization strategies
- Funding requirements and runway analysis
- Break-even analysis and path to profitability

Assume a lean startup approach with focus on capital efficiency and sustainable growth.`;
  }

  private buildPerformanceMetricsPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    return `Define comprehensive performance metrics and KPIs for MVP success measurement:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Establish a framework for measuring MVP success and business viability.

Focus on:
1. PERFORMANCE METRICS: Key metrics that indicate product and business success
2. SUCCESS CRITERIA: Specific targets and thresholds for each metric
3. KPI FRAMEWORK: Organized approach to metric tracking and analysis
4. MEASUREMENT STRATEGY: How and when to collect and analyze data
5. ANALYTICS IMPLEMENTATION: Tools and processes for data collection

Include metrics for:
- Product adoption and user engagement
- Customer acquisition and retention
- Revenue and business model validation
- Product-market fit indicators
- Operational efficiency and costs

For each metric, specify:
- Metric name and definition
- Target value and timeframe
- Measurement method and frequency
- Category (product, business, operational)
- Priority level (high, medium, low)

Ensure metrics are:
- Actionable and directly tied to business decisions
- Measurable with available tools and resources
- Relevant to MVP stage and business model
- Balanced across leading and lagging indicators

Focus on metrics that guide product development, validate business assumptions, and demonstrate progress to stakeholders.`;
  }

  private async buildIntroductionPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    
    // Validar que questions sea un array antes de usar filter y map
    const answeredQuestions = Array.isArray(questions) 
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all MVP analyses
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const mvpAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('mvp_'))
      : [];

    let allAnalysesContext = '';
    if (Array.isArray(mvpAnalyses)) {
      mvpAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create a comprehensive introduction for the Path to MVP analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

ALL MVP ANALYSES COMPLETED:${allAnalysesContext}

TASK: Create an executive summary that synthesizes all Path to MVP analyses.

Include:
1. EXECUTIVE SUMMARY: Overview of the MVP strategy and approach
2. KEY FINDINGS: Most important insights from all MVP analyses
3. STRATEGIC OPPORTUNITIES: Main opportunities for MVP success
4. MAIN CHALLENGES: Key challenges and risks in MVP development
5. OVERALL ASSESSMENT: Viability and potential of the MVP approach
6. NEXT STEPS: Recommended immediate actions and priorities
7. RECOMMENDED APPROACH: Strategic recommendations for MVP execution

Provide a clear, compelling introduction that:
- Communicates the MVP strategy and rationale
- Highlights critical success factors and risks
- Provides actionable recommendations for execution
- Sets realistic expectations for timeline and resources

Synthesize all completed MVP analyses into a coherent strategic overview that guides decision-making and execution.`;
  }

  private updateProgress(progress: MVPGeneratorProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}