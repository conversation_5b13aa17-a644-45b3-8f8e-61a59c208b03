import { AIService } from './aiService';
import { DatabaseService } from '../supabase';
import { responseSchemas } from './systemPrompts';

export interface FinancesProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: string[];
  isGenerating: boolean;
  error?: string;
}

export class FinancesService {
  private researchId: string;
  private projectData: any;
  private onProgress?: (progress: FinancesProgress) => void;

  constructor(researchId: string, projectData: any, onProgress?: (progress: FinancesProgress) => void) {
    this.researchId = researchId;
    this.projectData = projectData;
    this.onProgress = onProgress;
  }

  async generateCompleteFinancialAnalysis(): Promise<void> {
    const steps = [
      'market_research',
      'startup_costs',
      'revenue_projection',
      'operating_expenses',
      'breakeven_analysis',
      'funding_risks',
      'kpis',
      'introduction'
    ];

    const completedSteps: string[] = [];

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: true
        });

        // Generate analysis for current step
        await this.generateAnalysis(step);
        
        // Mark step as completed
        completedSteps.push(step);
        
        // Update progress
        this.updateProgress({
          currentStep: step,
          totalSteps: steps.length,
          completedSteps: [...completedSteps],
          isGenerating: false
        });
      }

      // Final progress update
      this.updateProgress({
        currentStep: 'completed',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false
      });

    } catch (error) {
      this.updateProgress({
        currentStep: 'error',
        totalSteps: steps.length,
        completedSteps: [...completedSteps],
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async generateAnalysis(analysisType: string): Promise<any> {
    let aiService: AIService;
    let prompt: string;
    let schema: any;

    // Configure AI service based on analysis type
    switch (analysisType) {
      case 'market_research':
        aiService = new AIService({
          section: 'finances_market_research',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.financesMarketResearch,
          temperature: 0.7
        });
        prompt = this.buildMarketResearchPrompt();
        schema = responseSchemas.financesMarketResearch;
        break;

      case 'startup_costs':
        aiService = new AIService({
          section: 'finances_startup_costs',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.financesStartupCosts,
          temperature: 0.7
        });
        prompt = await this.buildStartupCostsPrompt();
        schema = responseSchemas.financesStartupCosts;
        break;

      case 'revenue_projection':
        aiService = new AIService({
          section: 'finances_revenue_projection',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.financesRevenueProjection,
          temperature: 0.7
        });
        prompt = await this.buildRevenueProjectionPrompt();
        schema = responseSchemas.financesRevenueProjection;
        break;

      case 'operating_expenses':
        aiService = new AIService({
          section: 'finances_operating_expenses',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.financesOperatingExpenses,
          temperature: 0.7
        });
        prompt = await this.buildOperatingExpensesPrompt();
        schema = responseSchemas.financesOperatingExpenses;
        break;

      case 'breakeven_analysis':
        aiService = new AIService({
          section: 'finances_breakeven_analysis',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.financesBreakevenAnalysis,
          temperature: 0.7
        });
        prompt = await this.buildBreakevenAnalysisPrompt();
        schema = responseSchemas.financesBreakevenAnalysis;
        break;

      case 'funding_risks':
        aiService = new AIService({
          section: 'finances_funding_risks',
          useTools: true,
          responseFormat: 'json',
          schema: responseSchemas.financesFundingRisks,
          temperature: 0.7
        });
        prompt = await this.buildFundingRisksPrompt();
        schema = responseSchemas.financesFundingRisks;
        break;

      case 'kpis':
        aiService = new AIService({
          section: 'finances_kpis',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.financesKPIs,
          temperature: 0.7
        });
        prompt = await this.buildKPIsPrompt();
        schema = responseSchemas.financesKPIs;
        break;

      case 'introduction':
        aiService = new AIService({
          section: 'finances_introduction',
          useTools: false,
          responseFormat: 'json',
          schema: responseSchemas.financesIntroduction,
          temperature: 0.7
        });
        prompt = await this.buildIntroductionPrompt();
        schema = responseSchemas.financesIntroduction;
        break;

      default:
        throw new Error(`Unknown analysis type: ${analysisType}`);
    }

    // Generate analysis
    const response = await aiService.query(prompt);
    
    // Parse response
    let analysisData;
    if (response.type === 'text') {
      try {
        analysisData = JSON.parse(response.text);
      } catch (error) {
        throw new Error(`Failed to parse ${analysisType} analysis response`);
      }
    } else {
      analysisData = JSON.parse(response.text);
    }

    // Save to database
    await DatabaseService.saveResearchAnalysis(this.researchId, `finances_${analysisType}`, analysisData);

    return analysisData;
  }

  private buildMarketResearchPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    return `Conduct comprehensive financial market research for this business idea:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

TASK: Research and analyze the financial market landscape for this business opportunity.

Use web research to gather current data on:
1. TOTAL MARKET SIZE: Global and regional market size with growth rates
2. TARGET MARKET: Serviceable addressable market and obtainable market
3. COMPETITIVE ANALYSIS: Financial performance of key competitors, pricing models, revenue streams
4. INDUSTRY BENCHMARKS: Financial metrics, profitability ratios, typical business models
5. FUNDING LANDSCAPE: Investment trends, typical funding rounds, investor preferences
6. REVENUE MODELS: Successful monetization strategies in this industry

Focus on:
- Current market size and growth projections (TAM, SAM, SOM)
- Competitor financial performance and business models
- Industry financial benchmarks and KPIs
- Pricing strategies and revenue models
- Investment and funding trends
- Market opportunities and financial potential

Provide specific, research-backed financial insights with sources and current market data.`;
  }

  private async buildStartupCostsPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    if (!research || !research.title || !research.project_type || !research.description) {
      throw new Error('Project data is missing required fields');
    }
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get previous analyses for context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const mvpAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section.startsWith('mvp_'))
      : [];
    const strategicAnalyses = analyses.filter(a => a.section.startsWith('strategic_'));
    
    let contextData = '';
    if (Array.isArray(mvpAnalyses)) {
      mvpAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }
    if (Array.isArray(strategicAnalyses)) {
      strategicAnalyses.forEach(analysis => {
        contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
      });
    }

    return `Calculate detailed startup costs and investment requirements for this business:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

PREVIOUS ANALYSIS CONTEXT:${contextData}

TASK: Develop comprehensive startup cost analysis based on the business requirements and MVP planning.

Calculate costs for:
1. PLATFORM DEVELOPMENT: Engineering, design, infrastructure, testing costs
2. MARKETING CAMPAIGN: Customer acquisition, content creation, advertising, events
3. CONTENT CREATION: Documentation, tutorials, demos, educational materials
4. INITIAL STAFFING: First 6-12 months operational costs, key hires
5. LEGAL & COMPLIANCE: Business formation, IP protection, regulatory compliance
6. INFRASTRUCTURE: Technology stack, hosting, software licenses, tools
7. OFFICE & EQUIPMENT: Physical space, equipment, administrative setup

For each category, provide:
- Detailed cost breakdown with specific line items
- Justification for cost estimates
- Timeline for when costs will be incurred
- Critical vs. optional expenses
- Cost optimization opportunities

Include:
- Total investment required for 6-month and 12-month runway
- Phased investment approach (MVP, growth, scale phases)
- Contingency planning (10-20% buffer)
- Cost sensitivity analysis
- Funding milestone requirements

Base estimates on realistic market rates, team size requirements from MVP analysis, and industry standards. Consider both one-time and recurring costs.`;
  }

  private async buildRevenueProjectionPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    if (!research || !research.title || !research.project_type || !research.description) {
      throw new Error('Project data is missing required fields');
    }
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get relevant analyses for context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const allAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && (a.section.startsWith('mvp_') || a.section.startsWith('strategic_') || a.section.startsWith('usp_') || a.section.startsWith('finances_market_research')))
      : [];
    
    let contextData = '';
    if (Array.isArray(allAnalyses)) {
      allAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create detailed revenue projections and financial forecasts for this business:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

BUSINESS ANALYSIS CONTEXT:${contextData}

TASK: Develop comprehensive revenue projections based on market analysis, business model, and growth strategy.

Create projections for:
1. REVENUE STREAMS: Primary and secondary revenue sources with detailed modeling
2. PRICING STRATEGY: Pricing tiers, models, and evolution over time
3. CUSTOMER ACQUISITION: User growth, conversion rates, customer lifetime value
4. MARKET PENETRATION: Market share growth and competitive positioning
5. SEASONAL PATTERNS: Revenue seasonality and cyclical factors
6. GROWTH SCENARIOS: Conservative, realistic, and optimistic projections

Provide monthly projections for:
- Year 1: Monthly breakdown with key assumptions
- Year 2-3: Quarterly projections with growth drivers
- Year 4-5: Annual projections with market expansion

For each revenue stream, include:
- Customer segments and pricing models
- Unit economics (ARPU, LTV, CAC)
- Growth assumptions and drivers
- Market penetration rates
- Competitive impact factors

Include key metrics:
- Monthly Recurring Revenue (MRR) growth
- Annual Recurring Revenue (ARR) projections
- Customer acquisition and retention rates
- Average Revenue Per User (ARPU)
- Gross margin and unit economics
- Market share progression

Base projections on:
- Market research data and industry benchmarks
- Competitive analysis and positioning
- Customer persona insights and demand validation
- MVP features and value proposition strength
- Go-to-market strategy effectiveness

Provide realistic, data-driven projections with clear assumptions and sensitivity analysis.`;
  }

  private async buildOperatingExpensesPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    if (!research || !research.title || !research.project_type || !research.description) {
      throw new Error('Project data is missing required fields');
    }
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get startup costs and other relevant analyses
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const relevantAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && (a.section.includes('startup_costs') || a.section.startsWith('mvp_') || a.section.includes('market_research')))
      : [];
    
    let contextData = '';
    if (Array.isArray(relevantAnalyses)) {
      relevantAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Calculate comprehensive operating expenses and ongoing costs for this business:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

STARTUP AND BUSINESS CONTEXT:${contextData}

TASK: Develop detailed operating expense model for ongoing business operations.

Calculate ongoing expenses for:
1. PERSONNEL COSTS: Salaries, benefits, contractors, equity compensation
2. TECHNOLOGY COSTS: Infrastructure, software licenses, development tools, hosting
3. MARKETING & SALES: Customer acquisition, content marketing, advertising, events
4. OPERATIONS: Office rent, utilities, insurance, legal, accounting
5. RESEARCH & DEVELOPMENT: Product development, innovation, technical infrastructure
6. CUSTOMER SUCCESS: Support, onboarding, retention programs
7. ADMINISTRATIVE: Management, HR, finance, compliance, miscellaneous

For each category, provide:
- Monthly and annual cost projections
- Fixed vs. variable cost breakdown
- Scaling assumptions as business grows
- Cost optimization opportunities
- Industry benchmark comparisons

Include detailed analysis of:
- Cost structure evolution over 5 years
- Variable costs tied to revenue/customer growth
- Fixed costs and operational leverage
- Cost per customer acquisition and retention
- Operational efficiency metrics
- Break-even analysis inputs

Provide projections for:
- Year 1: Monthly operating expense breakdown
- Year 2-3: Quarterly projections with scaling factors
- Year 4-5: Annual projections with operational maturity

Consider:
- Team growth plans and hiring timeline
- Technology scaling requirements
- Marketing spend efficiency and ROI
- Operational leverage and economies of scale
- Industry-specific cost factors
- Geographic expansion costs

Base estimates on realistic market rates, industry benchmarks, and business growth requirements from previous analyses.`;
  }

  private async buildBreakevenAnalysisPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    if (!research || !research.title || !research.project_type || !research.description) {
      throw new Error('Project data is missing required fields');
    }
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get revenue and expense analyses
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const financialAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && (a.section.includes('revenue_projection') || a.section.includes('operating_expenses') || a.section.includes('startup_costs')))
      : [];
    
    let contextData = '';
    if (Array.isArray(financialAnalyses)) {
      financialAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Conduct comprehensive breakeven analysis and profitability modeling:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

FINANCIAL PROJECTIONS CONTEXT:${contextData}

TASK: Analyze breakeven points, profitability timeline, and financial sustainability.

Calculate and analyze:
1. BREAKEVEN ANALYSIS: Revenue required to cover all costs
2. UNIT ECONOMICS: Customer-level profitability and payback periods
3. CASH FLOW ANALYSIS: Monthly cash flow projections and runway
4. PROFITABILITY TIMELINE: Path to profitability and key milestones
5. SENSITIVITY ANALYSIS: Impact of key variables on breakeven
6. SCENARIO MODELING: Best case, worst case, and realistic scenarios

Provide detailed breakeven analysis:
- Monthly breakeven revenue requirements
- Customer breakeven (number of customers needed)
- Unit breakeven (revenue per customer needed)
- Time to breakeven under different growth scenarios
- Cash flow breakeven vs. accounting breakeven

Include unit economics analysis:
- Customer Acquisition Cost (CAC)
- Customer Lifetime Value (LTV)
- LTV/CAC ratio and payback period
- Gross margin per customer
- Contribution margin analysis

Analyze cash flow dynamics:
- Monthly cash burn rate
- Cash flow positive timeline
- Working capital requirements
- Seasonal cash flow patterns
- Funding requirement milestones

Provide scenario analysis:
- Conservative scenario (slower growth, higher costs)
- Realistic scenario (base case projections)
- Optimistic scenario (faster growth, operational efficiency)
- Stress test scenarios (market downturns, competitive pressure)

Include key insights:
- Critical success factors for breakeven
- Operational leverage opportunities
- Risk factors that could delay profitability
- Strategic recommendations for faster breakeven
- Funding requirements and runway analysis

Base analysis on the revenue projections and operating expense models from previous analyses.`;
  }

  private async buildFundingRisksPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    if (!research || !research.title || !research.project_type || !research.description) {
      throw new Error('Project data is missing required fields');
    }
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all financial analyses for context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const allFinancialAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('finances_'))
      : [];
    const strategicAnalyses = analyses.filter(a => a.section.startsWith('strategic_'));
    
    let contextData = '';
    if (Array.isArray(allFinancialAnalyses)) {
      allFinancialAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }
    if (Array.isArray(strategicAnalyses)) {
      strategicAnalyses.forEach(analysis => {
        contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
      });
    }

    return `Analyze funding options and comprehensive risk assessment for this business:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

COMPREHENSIVE BUSINESS ANALYSIS:${contextData}

TASK: Evaluate funding strategies and conduct thorough risk analysis.

Use web research to analyze:
1. FUNDING LANDSCAPE: Current investment trends, typical funding rounds, investor preferences
2. INDUSTRY FUNDING: Successful funding examples, valuation benchmarks, investor focus areas
3. RISK FACTORS: Market risks, competitive threats, operational challenges
4. MITIGATION STRATEGIES: Risk reduction approaches and contingency planning

Analyze funding options:
1. BOOTSTRAPPING: Self-funding approach, timeline, and limitations
2. ANGEL INVESTMENT: Angel investor landscape, typical terms, and requirements
3. VENTURE CAPITAL: VC funding stages, requirements, and market conditions
4. ALTERNATIVE FUNDING: Crowdfunding, grants, debt financing, revenue-based financing
5. STRATEGIC PARTNERSHIPS: Corporate partnerships and strategic investments

For each funding option, provide:
- Funding amount and timeline
- Equity dilution and terms
- Requirements and qualifications
- Pros and cons for this specific business
- Success probability and market conditions

Conduct comprehensive risk analysis:
1. MARKET RISKS: Market size, demand validation, competitive threats
2. TECHNOLOGY RISKS: Technical feasibility, development challenges, scalability
3. TEAM RISKS: Key person dependency, skill gaps, execution capability
4. FINANCIAL RISKS: Cash flow, profitability timeline, funding availability
5. OPERATIONAL RISKS: Scaling challenges, operational complexity, regulatory issues
6. COMPETITIVE RISKS: Market entry, competitive response, differentiation sustainability

For each risk, provide:
- Risk description and potential impact
- Probability assessment (High/Medium/Low)
- Mitigation strategies and contingency plans
- Early warning indicators
- Cost of mitigation vs. cost of risk materialization

Include strategic recommendations:
- Optimal funding strategy and timeline
- Risk prioritization and mitigation roadmap
- Contingency planning for key scenarios
- Investor targeting and preparation requirements
- Due diligence preparation and key metrics

Research current market conditions, successful funding examples in the industry, and investor preferences to provide realistic funding guidance.`;
  }

  private async buildKPIsPrompt(): string {
    const { research, questions } = this.projectData;
    const answeredQuestions = questions.filter((q: any) => q.answer).map((q: any) => `Q: ${q.question}\nA: ${q.answer}`).join('\n\n');

    // Get all previous analyses for comprehensive context
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId);
    const allAnalyses = analyses.filter(a => 
      a.section.startsWith('finances_') || 
      a.section.startsWith('mvp_') ||
      a.section.startsWith('strategic_')
    );
    
    let contextData = '';
    allAnalyses.forEach(analysis => {
      contextData += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
    });

    return `Define comprehensive Key Performance Indicators (KPIs) and financial metrics framework:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

COMPREHENSIVE BUSINESS ANALYSIS:${contextData}

TASK: Establish a complete KPI framework for measuring business success and financial performance.

Define KPIs across key categories:
1. FINANCIAL METRICS: Revenue, profitability, cash flow, unit economics
2. CUSTOMER METRICS: Acquisition, retention, satisfaction, lifetime value
3. PRODUCT METRICS: Usage, engagement, feature adoption, performance
4. OPERATIONAL METRICS: Efficiency, productivity, quality, scalability
5. MARKET METRICS: Market share, competitive position, brand awareness
6. TEAM METRICS: Performance, retention, productivity, satisfaction

For each KPI, provide:
- Metric name and clear definition
- Current baseline (if applicable) and target values
- Measurement frequency and methodology
- Responsible team/person for tracking
- Industry benchmarks and best practices
- Relationship to business objectives

Include specific financial KPIs:
- Monthly Recurring Revenue (MRR) and growth rate
- Customer Acquisition Cost (CAC) and trends
- Customer Lifetime Value (LTV) and LTV/CAC ratio
- Gross margin and contribution margin
- Cash burn rate and runway
- Revenue per employee and operational efficiency

Include customer and product KPIs:
- Customer acquisition and activation rates
- Customer retention and churn rates
- Net Promoter Score (NPS) and satisfaction
- Product usage and engagement metrics
- Feature adoption and user behavior
- Support ticket volume and resolution time

Provide KPI framework structure:
- North Star metrics (primary success indicators)
- Leading indicators (predictive metrics)
- Lagging indicators (outcome metrics)
- Operational dashboards and reporting cadence
- Alert thresholds and escalation procedures

Include implementation guidance:
- Data collection and measurement systems
- Reporting tools and dashboard requirements
- Review cycles and decision-making processes
- KPI evolution as business matures
- Benchmarking and competitive analysis

Ensure KPIs are:
- Specific, measurable, and actionable
- Aligned with business strategy and goals
- Balanced across different business aspects
- Realistic and achievable given resources
- Relevant to stakeholders and decision-making

Base KPI selection on the business model, revenue projections, and strategic objectives identified in previous analyses.`;
  }

  private async buildIntroductionPrompt(): string {
    const { research, questions = [] } = this.projectData || {};
    const answeredQuestions = Array.isArray(questions)
      ? questions.filter((q: any) => q && q.answer && q.question)
               .map((q: any) => `Q: ${q.question}\nA: ${q.answer}`)
               .join('\n\n')
      : '';

    // Get all financial analyses
    const analyses = await DatabaseService.getResearchAnalysis(this.researchId) || [];
    const financialAnalyses = Array.isArray(analyses)
      ? analyses.filter(a => a && typeof a === 'object' && a.section && a.section.startsWith('finances_'))
      : [];

    let allAnalysesContext = '';
    if (Array.isArray(financialAnalyses)) {
      financialAnalyses.forEach(analysis => {
        if (analysis && analysis.section && analysis.analysis_data) {
          allAnalysesContext += `\n\n${analysis.section.toUpperCase()}:\n${JSON.stringify(analysis.analysis_data, null, 2)}`;
        }
      });
    }

    return `Create a comprehensive introduction for the Financial Analysis:

PROJECT INFORMATION:
Title: ${research.title}
Type: ${research.project_type}
Description: ${research.description}
Industry: ${research.industry || 'Not specified'}

ADDITIONAL PROJECT DETAILS:
${answeredQuestions}

ALL FINANCIAL ANALYSES COMPLETED:${allAnalysesContext}

TASK: Create an executive summary that synthesizes all financial analyses.

Include:
1. EXECUTIVE SUMMARY: Overview of the financial analysis and business viability
2. KEY FINDINGS: Most important financial insights and opportunities
3. FINANCIAL VIABILITY: Assessment of business financial potential and sustainability
4. INVESTMENT REQUIREMENTS: Total funding needs and investment timeline
5. PROFITABILITY OUTLOOK: Path to profitability and financial milestones
6. RISK ASSESSMENT: Key financial risks and mitigation strategies
7. STRATEGIC RECOMMENDATIONS: Financial strategy and next steps

Provide a clear, compelling introduction that:
- Communicates the financial opportunity and viability
- Highlights key financial metrics and projections
- Addresses investment requirements and returns
- Identifies critical success factors and risks
- Provides actionable financial recommendations

Synthesize insights from:
- Market research and financial opportunity
- Startup costs and investment requirements
- Revenue projections and growth potential
- Operating expenses and cost structure
- Breakeven analysis and profitability timeline
- Funding options and risk assessment
- KPIs and performance measurement

Focus on creating a financial overview that guides investment decisions, strategic planning, and operational execution while clearly communicating the business financial potential and requirements.`;
  }

  private updateProgress(progress: FinancesProgress): void {
    if (this.onProgress) {
      this.onProgress(progress);
    }
  }
}