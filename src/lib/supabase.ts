import { createClient } from '@supabase/supabase-js';
import type { User } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Auth functions
export const auth = {
  async signUp(email: string, password: string, name?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: name,
        },
      },
    });
    
    if (error) throw error;
    return data;
  },

  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) throw error;
    return data;
  },

  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },

  onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange((event, session) => {
      callback(session?.user || null);
    });
  }
};

// Types for our database tables
export interface MarketResearch {
  id: string;
  user_id: string;
  title: string;
  project_type: 'new-idea' | 'existing-project';
  description: string;
  industry?: string;
  status: 'draft' | 'in-progress' | 'completed';
  viability_score?: number;
  created_at: string;
  updated_at: string;
}

export interface ResearchQuestion {
  id: string;
  research_id: string;
  question: string;
  answer?: string;
  order_index: number;
  created_at: string;
}

export interface ResearchAnalysis {
  id: string;
  research_id: string;
  section: string;
  analysis_data: any;
  created_at: string;
  updated_at: string;
}

// Database service class
export class DatabaseService {
  // Auth methods
  static async signUp(email: string, password: string, name?: string) {
    return auth.signUp(email, password, name);
  }

  static async signIn(email: string, password: string) {
    return auth.signIn(email, password);
  }

  static async signOut() {
    return auth.signOut();
  }

  static async getCurrentUser() {
    return auth.getCurrentUser();
  }

  static onAuthStateChange(callback: (user: User | null) => void) {
    return auth.onAuthStateChange(callback);
  }

  // Market Research CRUD operations
  static async createMarketResearch(data: Omit<MarketResearch, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<MarketResearch> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const { data: research, error } = await supabase
      .from('market_researches')
      .insert({
        ...data,
        user_id: user.id
      })
      .select()
      .single();

    if (error) throw error;
    return research;
  }

  static async getMarketResearches(): Promise<MarketResearch[]> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('market_researches')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getMarketResearch(id: string): Promise<MarketResearch | null> {
    const { data, error } = await supabase
      .from('market_researches')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }

  static async updateMarketResearch(id: string, updates: Partial<MarketResearch>): Promise<MarketResearch> {
    const { data, error } = await supabase
      .from('market_researches')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteMarketResearch(id: string): Promise<void> {
    const { error } = await supabase
      .from('market_researches')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Research Questions CRUD operations
  static async createResearchQuestions(researchId: string, questions: string[]): Promise<ResearchQuestion[]> {
    const questionsData = questions.map((question, index) => ({
      research_id: researchId,
      question,
      order_index: index
    }));

    const { data, error } = await supabase
      .from('research_questions')
      .insert(questionsData)
      .select();

    if (error) throw error;
    return data || [];
  }

  static async getResearchQuestions(researchId: string): Promise<ResearchQuestion[]> {
    const { data, error } = await supabase
      .from('research_questions')
      .select('*')
      .eq('research_id', researchId)
      .order('order_index');

    if (error) throw error;
    return data || [];
  }

  static async updateResearchQuestion(id: string, updates: Partial<ResearchQuestion>): Promise<ResearchQuestion> {
    const { data, error } = await supabase
      .from('research_questions')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateQuestionAnswer(questionId: string, answer: string): Promise<ResearchQuestion> {
    return this.updateResearchQuestion(questionId, { answer });
  }

  // Research Analysis CRUD operations
  static async saveResearchAnalysis(researchId: string, section: string, analysisData: any): Promise<ResearchAnalysis> {
    // First try to update existing analysis
    const { data: existing } = await supabase
      .from('research_analysis')
      .select('id')
      .eq('research_id', researchId)
      .eq('section', section)
      .single();

    if (existing) {
      // Update existing
      const { data, error } = await supabase
        .from('research_analysis')
        .update({ analysis_data: analysisData })
        .eq('id', existing.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } else {
      // Create new
      const { data, error } = await supabase
        .from('research_analysis')
        .insert({
          research_id: researchId,
          section,
          analysis_data: analysisData
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    }
  }

  static async getResearchAnalysis(researchId: string, section?: string): Promise<ResearchAnalysis[]> {
    let query = supabase
      .from('research_analysis')
      .select('*')
      .eq('research_id', researchId);

    if (section) {
      query = query.eq('section', section);
    }

    const { data, error } = await query.order('created_at');

    if (error) throw error;
    return data || [];
  }

  // Utility methods
  static async getResearchWithQuestions(researchId: string): Promise<{
    research: MarketResearch;
    questions: ResearchQuestion[];
  } | null> {
    const research = await this.getMarketResearch(researchId);
    if (!research) return null;

    const questions = await this.getResearchQuestions(researchId);

    return { research, questions };
  }

  static async getCompleteResearch(researchId: string): Promise<{
    research: MarketResearch;
    questions: ResearchQuestion[];
    analysis: ResearchAnalysis[];
  } | null> {
    const research = await this.getMarketResearch(researchId);
    if (!research) return null;

    const [questions, analysis] = await Promise.all([
      this.getResearchQuestions(researchId),
      this.getResearchAnalysis(researchId)
    ]);

    return { research, questions, analysis };
  }
}