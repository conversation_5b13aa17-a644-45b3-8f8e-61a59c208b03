import { useState, useCallback } from 'react';
import { AIService } from '../lib/ai/aiService';

export interface UseAIOptions {
  section?: string;
  useTools?: boolean;
  responseFormat?: 'text' | 'json';
  schema?: any;
  temperature?: number;
}

export function useAI(options: UseAIOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [aiService] = useState(() => new AIService(options));

  const query = useCallback(async (prompt: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await aiService.query(prompt);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [aiService]);

  const startConversation = useCallback(async (initialMessage?: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await aiService.startConversation(initialMessage);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [aiService]);

  const sendMessage = useCallback(async (message: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await aiService.sendMessage(message);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [aiService]);

  const switchSection = useCallback((section: string, config: Partial<UseAIOptions> = {}) => {
    aiService.switchSection(section, config);
  }, [aiService]);

  const analyzeMarketSize = useCallback(async (businessIdea: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await aiService.analyzeMarketSize(businessIdea);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [aiService]);

  const generateSWOT = useCallback(async (businessIdea: string, industry: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await aiService.generateSWOT(businessIdea, industry);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [aiService]);

  const createCustomerPersona = useCallback(async (businessIdea: string, targetMarket: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await aiService.createCustomerPersona(businessIdea, targetMarket);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [aiService]);

  return {
    isLoading,
    error,
    query,
    startConversation,
    sendMessage,
    switchSection,
    analyzeMarketSize,
    generateSWOT,
    createCustomerPersona,
    aiService
  };
}