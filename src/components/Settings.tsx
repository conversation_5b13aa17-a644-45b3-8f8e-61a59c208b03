import React, { useState } from 'react';
import { User, CreditCard, Save, Eye, EyeOff, Building, Plus, Mail, Trash2, Edit, UserPlus, Crown, Shield } from 'lucide-react';

export function Settings() {
  const [activeSection, setActiveSection] = useState('personal');
  const [showPassword, setShowPassword] = useState(false);
  const [showCreateCorporateModal, setShowCreateCorporateModal] = useState(false);
  const [showInviteUserModal, setShowInviteUserModal] = useState(false);
  const [selectedCorporateAccount, setSelectedCorporateAccount] = useState(null);
  const [newCorporateAccount, setNewCorporateAccount] = useState({
    name: '',
    description: '',
    industry: ''
  });
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('member');
  const [personalData, setPersonalData] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Mock data for corporate accounts
  const [corporateAccounts, setCorporateAccounts] = useState([
    {
      id: 'acme-corp',
      name: 'Acme Corporation',
      description: 'Leading technology solutions provider',
      industry: 'Technology',
      createdAt: '2024-01-15',
      members: [
        { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'admin', status: 'active' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'member', status: 'active' },
        { id: 3, name: 'Mike Johnson', email: '<EMAIL>', role: 'member', status: 'pending' }
      ]
    },
    {
      id: 'tech-startup',
      name: 'Tech Startup Inc.',
      description: 'Innovative startup focused on AI solutions',
      industry: 'Artificial Intelligence',
      createdAt: '2024-02-20',
      members: [
        { id: 4, name: 'John Doe', email: '<EMAIL>', role: 'admin', status: 'active' },
        { id: 5, name: 'Sarah Wilson', email: '<EMAIL>', role: 'member', status: 'active' }
      ]
    }
  ]);
  const handleSave = () => {
    // Handle save logic here
    console.log('Saving settings...');
  };

  const handleCreateCorporateAccount = () => {
    const newAccount = {
      id: newCorporateAccount.name.toLowerCase().replace(/\s+/g, '-'),
      ...newCorporateAccount,
      createdAt: new Date().toISOString().split('T')[0],
      members: [
        { id: Date.now(), name: 'John Doe', email: '<EMAIL>', role: 'admin', status: 'active' }
      ]
    };
    setCorporateAccounts([...corporateAccounts, newAccount]);
    setNewCorporateAccount({ name: '', description: '', industry: '' });
    setShowCreateCorporateModal(false);
  };

  const handleInviteUser = () => {
    if (selectedCorporateAccount && inviteEmail) {
      const updatedAccounts = corporateAccounts.map(account => {
        if (account.id === selectedCorporateAccount.id) {
          return {
            ...account,
            members: [
              ...account.members,
              {
                id: Date.now(),
                name: inviteEmail.split('@')[0],
                email: inviteEmail,
                role: inviteRole,
                status: 'pending'
              }
            ]
          };
        }
        return account;
      });
      setCorporateAccounts(updatedAccounts);
      setInviteEmail('');
      setInviteRole('member');
      setShowInviteUserModal(false);
    }
  };

  const handleRemoveUser = (accountId, userId) => {
    const updatedAccounts = corporateAccounts.map(account => {
      if (account.id === accountId) {
        return {
          ...account,
          members: account.members.filter(member => member.id !== userId)
        };
      }
      return account;
    });
    setCorporateAccounts(updatedAccounts);
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'admin': return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'manager': return <Shield className="w-4 h-4 text-blue-500" />;
      default: return <User className="w-4 h-4 text-gray-500" />;
    }
  };

  const getRoleBadgeColor = (role) => {
    switch (role) {
      case 'admin': return 'bg-yellow-100 text-yellow-800';
      case 'manager': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Settings</h1>
        <p className="text-gray-600">
          Manage your account settings and preferences.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
            <nav className="space-y-2">
              <button
                onClick={() => setActiveSection('personal')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === 'personal'
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <User className={`w-4 h-4 ${activeSection === 'personal' ? 'text-blue-500' : 'text-gray-400'}`} />
                <span>Personal</span>
              </button>
              
              <button
                onClick={() => setActiveSection('billing')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === 'billing'
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <CreditCard className={`w-4 h-4 ${activeSection === 'billing' ? 'text-blue-500' : 'text-gray-400'}`} />
                <span>Billing</span>
              </button>
              
              <button
                onClick={() => setActiveSection('corporate')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === 'corporate'
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <Building className={`w-4 h-4 ${activeSection === 'corporate' ? 'text-blue-500' : 'text-gray-400'}`} />
                <span>Cuentas Corporativas</span>
              </button>
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          {activeSection === 'personal' && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Personal Information</h2>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={personalData.name}
                      onChange={(e) => setPersonalData({...personalData, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={personalData.email}
                      onChange={(e) => setPersonalData({...personalData, email: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Current Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? "text" : "password"}
                          value={personalData.currentPassword}
                          onChange={(e) => setPersonalData({...personalData, currentPassword: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          New Password
                        </label>
                        <input
                          type="password"
                          value={personalData.newPassword}
                          onChange={(e) => setPersonalData({...personalData, newPassword: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Confirm New Password
                        </label>
                        <input
                          type="password"
                          value={personalData.confirmPassword}
                          onChange={(e) => setPersonalData({...personalData, confirmPassword: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end pt-6">
                  <button
                    onClick={handleSave}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'billing' && (
            <div className="space-y-6">
              {/* Current Plan */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Current Plan</h2>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-green-900">Free Plan</h3>
                      <p className="text-green-700">3 market researches per month</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-900">$0</div>
                      <div className="text-green-700 text-sm">forever</div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4">
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    Upgrade Plan
                  </button>
                </div>
              </div>

              {/* Usage */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Usage This Month</h2>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Market Researches</span>
                    <span className="font-medium">2 / 3</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '66%' }}></div>
                  </div>
                  <p className="text-sm text-gray-500">1 research remaining this month</p>
                </div>
              </div>

              {/* Billing History */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Billing History</h2>
                
                <div className="text-center py-8">
                  <CreditCard className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No billing history available</p>
                  <p className="text-sm text-gray-400">You're currently on the free plan</p>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'corporate' && (
            <div className="space-y-6">
              {/* Header with Create Button */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">Cuentas Corporativas</h2>
                    <p className="text-gray-600 mt-1">Gestiona tus cuentas corporativas y miembros del equipo</p>
                  </div>
                  <button
                    onClick={() => setShowCreateCorporateModal(true)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Crear Cuenta Corporativa
                  </button>
                </div>

                {/* Corporate Accounts List */}
                <div className="space-y-4">
                  {corporateAccounts.map((account) => (
                    <div key={account.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start space-x-3">
                          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <Building className="w-6 h-6 text-purple-600" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium text-gray-900">{account.name}</h3>
                            <p className="text-gray-600 text-sm">{account.description}</p>
                            <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                              <span>Industria: {account.industry}</span>
                              <span>Creado: {new Date(account.createdAt).toLocaleDateString()}</span>
                              <span>{account.members.length} miembros</span>
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            setSelectedCorporateAccount(account);
                            setShowInviteUserModal(true);
                          }}
                          className="px-3 py-1 bg-green-600 text-white rounded text-sm font-medium hover:bg-green-700 transition-colors flex items-center"
                        >
                          <UserPlus className="w-3 h-3 mr-1" />
                          Invitar Usuario
                        </button>
                      </div>

                      {/* Members List */}
                      <div className="border-t border-gray-200 pt-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-3">Miembros del Equipo</h4>
                        <div className="space-y-2">
                          {account.members.map((member) => (
                            <div key={member.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                  <User className="w-4 h-4 text-gray-600" />
                                </div>
                                <div>
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm font-medium text-gray-900">{member.name}</span>
                                    {getRoleIcon(member.role)}
                                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeColor(member.role)}`}>
                                      {member.role}
                                    </span>
                                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                      member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                    }`}>
                                      {member.status === 'active' ? 'Activo' : 'Pendiente'}
                                    </span>
                                  </div>
                                  <span className="text-xs text-gray-500">{member.email}</span>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <button className="p-1 text-gray-400 hover:text-blue-600 transition-colors">
                                  <Edit className="w-4 h-4" />
                                </button>
                                {member.role !== 'admin' && (
                                  <button 
                                    onClick={() => handleRemoveUser(account.id, member.id)}
                                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Corporate Account Modal */}
      {showCreateCorporateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Crear Cuenta Corporativa</h3>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre de la Empresa
                </label>
                <input
                  type="text"
                  value={newCorporateAccount.name}
                  onChange={(e) => setNewCorporateAccount({...newCorporateAccount, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ej: Acme Corporation"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descripción
                </label>
                <textarea
                  value={newCorporateAccount.description}
                  onChange={(e) => setNewCorporateAccount({...newCorporateAccount, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows="3"
                  placeholder="Breve descripción de la empresa"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Industria
                </label>
                <select
                  value={newCorporateAccount.industry}
                  onChange={(e) => setNewCorporateAccount({...newCorporateAccount, industry: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Seleccionar industria</option>
                  <option value="Technology">Tecnología</option>
                  <option value="Healthcare">Salud</option>
                  <option value="Finance">Finanzas</option>
                  <option value="Education">Educación</option>
                  <option value="Retail">Retail</option>
                  <option value="Manufacturing">Manufactura</option>
                  <option value="Other">Otro</option>
                </select>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowCreateCorporateModal(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateCorporateAccount}
                disabled={!newCorporateAccount.name || !newCorporateAccount.industry}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Crear Cuenta
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Invite User Modal */}
      {showInviteUserModal && selectedCorporateAccount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Invitar Usuario</h3>
              <p className="text-sm text-gray-600 mt-1">
                Invitar a {selectedCorporateAccount.name}
              </p>
            </div>
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Correo Electrónico
                </label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rol
                </label>
                <select
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="member">Miembro</option>
                  <option value="manager">Manager</option>
                  <option value="admin">Administrador</option>
                </select>
              </div>
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-start space-x-2">
                  <Mail className="w-4 h-4 text-blue-500 mt-0.5" />
                  <div className="text-sm text-blue-700">
                    <p className="font-medium">Se enviará una invitación por correo</p>
                    <p>El usuario recibirá un enlace para unirse a la cuenta corporativa.</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowInviteUserModal(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleInviteUser}
                disabled={!inviteEmail}
                className="px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                <Mail className="w-4 h-4 mr-2" />
                Enviar Invitación
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}