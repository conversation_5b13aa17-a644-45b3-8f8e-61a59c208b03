import React, { useState, useEffect } from 'react';
import { CheckCircle, Clock, DollarSign, Target, TrendingUp, Users, Loader2, AlertTriangle } from 'lucide-react';
import { DatabaseService } from '../lib/supabase';

interface PathToMVPProps {
  selectedResearch?: any;
}

export function PathToMVP({ selectedResearch }: PathToMVPProps) {
  const [mvpData, setMvpData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load MVP data
  useEffect(() => {
    const loadMVPData = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!selectedResearch) {
          setError('Please select a research to view MVP data');
          setLoading(false);
          return;
        }

        console.log('Loading MVP data for research:', selectedResearch.id);

        // Get MVP analyses
        const analyses = await DatabaseService.getResearchAnalysis(selectedResearch.id);
        const mvpAnalyses = analyses.filter(a => a.section.startsWith('mvp_'));

        if (mvpAnalyses.length === 0) {
          setError('MVP analysis not found for this research');
          setLoading(false);
          return;
        }

        // Organize data by section
        const organizedData: any = {};
        mvpAnalyses.forEach(analysis => {
          const sectionName = analysis.section.replace('mvp_', '');
          organizedData[sectionName] = analysis.analysis_data;
        });

        console.log('MVP data loaded:', organizedData);
        setMvpData(organizedData);

      } catch (error) {
        console.error('Error loading MVP data:', error);
        setError('Failed to load MVP data');
      } finally {
        setLoading(false);
      }
    };

    loadMVPData();
  }, [selectedResearch]);

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading MVP data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-gray-600 mb-6">{error}</p>
        </div>
      </div>
    );
  }

  // Helper function to render data
  const renderData = (data: any) => {
    if (!data) {
      return <div className="text-gray-500 italic">No data available</div>;
    }

    if (typeof data === 'string') {
      return <div className="text-gray-700 whitespace-pre-wrap">{data}</div>;
    }

    if (typeof data === 'object' && data !== null) {
      return (
        <div className="space-y-4">
          {Object.entries(data).map(([key, value]) => (
            <div key={key}>
              <h4 className="font-semibold text-gray-900 mb-2 capitalize">
                {key.replace(/_/g, ' ')}
              </h4>
              <div className="text-gray-700">
                {typeof value === 'string' ? (
                  <div className="whitespace-pre-wrap">{value}</div>
                ) : Array.isArray(value) ? (
                  <ul className="list-disc list-inside space-y-1">
                    {value.map((item, index) => (
                      <li key={index}>{typeof item === 'string' ? item : JSON.stringify(item)}</li>
                    ))}
                  </ul>
                ) : (
                  <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto">
                    {JSON.stringify(value, null, 2)}
                  </pre>
                )}
              </div>
            </div>
          ))}
        </div>
      );
    }

    return (
      <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto">
        {JSON.stringify(data, null, 2)}
      </pre>
    );
  };
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Path to MVP</h1>
        <p className="text-gray-600">
          {selectedResearch ?
            `Strategic roadmap for ${selectedResearch.title} from concept to market launch, including core feature development, market validation, marketing strategy, and key performance indicators.` :
            'Strategic roadmap from concept to market launch, including core feature development, market validation, marketing strategy, and key performance indicators.'
          }
        </p>
      </div>

      {/* MVP Analysis Data */}
      {mvpData && Object.keys(mvpData).length > 0 ? (
        Object.entries(mvpData).map(([sectionKey, sectionData]) => (
          <div key={sectionKey} className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 capitalize">
              {sectionKey.replace(/_/g, ' ')}
            </h2>
            <div className="space-y-4">
              {renderData(sectionData)}
            </div>
          </div>
        ))
      ) : (
        <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100 text-center">
          <Target className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No MVP Data Available</h3>
          <p className="text-gray-600">
            MVP analysis data is not available for this research. The analysis may still be generating or may have failed.
          </p>
        </div>
      )}
    </div>
  );
}
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Core Features Development</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">Feature</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Priority</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Development Time</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
              </tr>
            </thead>
            <tbody>
              {coreFeatures.map((item, index) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="font-medium text-gray-900">{item.feature}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.priority === 'High' ? 'bg-red-100 text-red-800' :
                      item.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {item.priority}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-600">{item.effort}</td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                      item.status === 'Planning' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {item.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Market Validation & Timeline */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Market Validation</h2>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">Customer Discovery</h3>
              <p className="text-sm text-green-700 mb-3">Entrevistas con 150+ developers confirmaron pain points clave</p>
              <ul className="text-xs text-green-600 space-y-1">
                <li>• 87% reporta frustración con debugging manual</li>
                <li>• 92% interesado en AI-assisted development</li>
                <li>• 78% dispuesto a pagar $50+/mes por productividad</li>
              </ul>
            </div>
            
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Competitive Analysis</h3>
              <p className="text-sm text-blue-700 mb-3">Análisis de 12 competidores directos e indirectos</p>
              <ul className="text-xs text-blue-600 space-y-1">
                <li>• GitHub Copilot: líder pero limitado en contexto</li>
                <li>• Tabnine: buena en autocomplete, débil en testing</li>
                <li>• Oportunidad clara en full-cycle development</li>
              </ul>
            </div>
            
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h3 className="font-semibold text-purple-900 mb-2">Product-Market Fit</h3>
              <p className="text-sm text-purple-700 mb-3">Métricas tempranas indican strong PMF potential</p>
              <ul className="text-xs text-purple-600 space-y-1">
                <li>• 94% satisfaction en early beta</li>
                <li>• 67% reporta "very disappointed" sin el producto</li>
                <li>• 45% organic growth en beta waitlist</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Timeline & Milestones</h2>
          <div className="space-y-4">
            {timeline.map((phase, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className={`w-4 h-4 rounded-full ${
                  phase.status === 'current' ? 'bg-blue-500' :
                  phase.status === 'completed' ? 'bg-green-500' :
                  'bg-gray-300'
                }`}></div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{phase.phase}</h3>
                  <p className="text-sm text-gray-500">{phase.duration}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">{phase.duration}</span>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Key Milestones</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">MVP Ready</span>
                <span className="font-medium text-gray-900">Week 12</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Beta Launch</span>
                <span className="font-medium text-gray-900">Week 16</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Public Launch</span>
                <span className="font-medium text-gray-900">Week 19</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Marketing Strategy */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Marketing Strategy</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {marketingStrategy.map((channel, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-3">{channel.channel}</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Reach:</span>
                  <span className="font-medium">{channel.reach}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Investment:</span>
                  <span className="font-medium text-orange-600">{channel.cost}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Expected ROI:</span>
                  <span className="font-medium text-green-600">{channel.roi}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Budget Considerations */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Budget Considerations</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
              <Target className="w-4 h-4 mr-2" />
              Development Costs
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-700">Engineering Team</span>
                <span className="font-medium">$180K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">Infrastructure</span>
                <span className="font-medium">$25K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">AI/ML Services</span>
                <span className="font-medium">$35K</span>
              </div>
              <div className="border-t border-blue-200 pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span className="text-blue-900">Total</span>
                  <span className="text-blue-900">$240K</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-3 flex items-center">
              <TrendingUp className="w-4 h-4 mr-2" />
              Marketing Costs
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-green-700">Digital Marketing</span>
                <span className="font-medium">$50K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Content Creation</span>
                <span className="font-medium">$20K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Events & Conferences</span>
                <span className="font-medium">$30K</span>
              </div>
              <div className="border-t border-green-200 pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span className="text-green-900">Total</span>
                  <span className="text-green-900">$100K</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <h3 className="font-semibold text-orange-900 mb-3 flex items-center">
              <DollarSign className="w-4 h-4 mr-2" />
              Operating Costs
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-orange-700">Legal & Compliance</span>
                <span className="font-medium">$15K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-orange-700">Office & Equipment</span>
                <span className="font-medium">$10K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-orange-700">Contingency (10%)</span>
                <span className="font-medium">$36K</span>
              </div>
              <div className="border-t border-orange-200 pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span className="text-orange-900">Total</span>
                  <span className="text-orange-900">$61K</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-gray-900 text-white rounded-lg">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Total MVP Investment Required</h3>
            <span className="text-2xl font-bold">$401K</span>
          </div>
          <p className="text-gray-300 text-sm mt-2">
            Investment covers 6-month runway to reach public launch and initial traction
          </p>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Performance Metrics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {performanceMetrics.map((metric, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-lg text-center">
              <h3 className="font-semibold text-gray-900 mb-2">{metric.metric}</h3>
              <div className="text-2xl font-bold text-blue-600 mb-1">{metric.target}</div>
              <div className="text-sm text-gray-500">{metric.timeframe}</div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-3">Success Criteria</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Product Metrics</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• 70% reduction in development time</li>
                <li>• 95% code accuracy rate</li>
                <li>• {'<'}100ms average response time</li>
                <li>• 99.9% platform uptime</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Business Metrics</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• $100K Monthly Recurring Revenue</li>
                <li>• 15% month-over-month growth</li>
                <li>• {'<'}5% monthly churn rate</li>
                <li>• Net Promoter Score {'>'}50</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}