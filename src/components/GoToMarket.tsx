import React from 'react';
import { Target, Users, DollarSign, TrendingUp, BarChart3, Globe, MessageSquare, Award } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';

const marketingChannels = [
  { channel: 'Developer Communities', reach: 500000, cost: 25000, roi: 320 },
  { channel: 'Content Marketing', reach: 200000, cost: 15000, roi: 450 },
  { channel: 'Tech Conferences', reach: 50000, cost: 75000, roi: 280 },
  { channel: 'Influencer Partnerships', reach: 150000, cost: 35000, roi: 380 },
  { channel: 'SEO & SEM', reach: 300000, cost: 20000, roi: 400 },
];

const growthProjection = [
  { month: 'M1', users: 0, revenue: 0 },
  { month: 'M2', users: 200, revenue: 7200 },
  { month: 'M3', users: 500, revenue: 18000 },
  { month: 'M4', users: 800, revenue: 28800 },
  { month: 'M5', users: 1200, revenue: 43200 },
  { month: 'M6', users: 1500, revenue: 54000 },
  { month: 'M7', users: 2000, revenue: 72000 },
  { month: 'M8', users: 2500, revenue: 90000 },
  { month: 'M9', users: 3000, revenue: 108000 },
  { month: 'M10', users: 3800, revenue: 136800 },
  { month: 'M11', users: 4500, revenue: 162000 },
  { month: 'M12', users: 5000, revenue: 180000 },
];

export function GoToMarket() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Go-to-Market Strategy</h1>
        <p className="text-gray-600">
          Comprehensive strategy for bringing CodeFlow AI to market, including target audience definition, 
          value proposition, pricing, distribution channels, marketing plan, and key performance indicators.
        </p>
      </div>

      {/* Go-To-Market Roadmap */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Go-To-Market Roadmap</h2>
        <div className="relative">
          <div className="absolute left-8 top-0 bottom-0 w-1 bg-blue-200"></div>
          
          <div className="space-y-8">
            <div className="relative flex">
              <div className="absolute left-8 top-8 -ml-4 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                <span className="text-white font-bold">1</span>
              </div>
              <div className="ml-16 p-6 bg-blue-50 rounded-lg border border-blue-200 flex-1">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Beta Launch (Month 1-3)</h3>
                <div className="space-y-2 text-sm text-blue-700">
                  <p>• Invite-only beta for 500 selected developers</p>
                  <p>• Focus on core AI code generation features</p>
                  <p>• Collect feedback and iterate rapidly</p>
                  <p>• Build waitlist and generate early buzz</p>
                </div>
              </div>
            </div>
            
            <div className="relative flex">
              <div className="absolute left-8 top-8 -ml-4 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                <span className="text-white font-bold">2</span>
              </div>
              <div className="ml-16 p-6 bg-blue-50 rounded-lg border border-blue-200 flex-1">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Public Launch (Month 4-6)</h3>
                <div className="space-y-2 text-sm text-blue-700">
                  <p>• Open access with freemium model</p>
                  <p>• Launch marketing campaign across developer channels</p>
                  <p>• Implement referral program for viral growth</p>
                  <p>• Begin content marketing strategy</p>
                </div>
              </div>
            </div>
            
            <div className="relative flex">
              <div className="absolute left-8 top-8 -ml-4 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                <span className="text-white font-bold">3</span>
              </div>
              <div className="ml-16 p-6 bg-blue-50 rounded-lg border border-blue-200 flex-1">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Growth Phase (Month 7-12)</h3>
                <div className="space-y-2 text-sm text-blue-700">
                  <p>• Scale marketing efforts across all channels</p>
                  <p>• Launch team/enterprise plans</p>
                  <p>• Develop strategic partnerships</p>
                  <p>• Expand feature set based on user feedback</p>
                </div>
              </div>
            </div>
            
            <div className="relative flex">
              <div className="absolute left-8 top-8 -ml-4 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                <span className="text-white font-bold">4</span>
              </div>
              <div className="ml-16 p-6 bg-blue-50 rounded-lg border border-blue-200 flex-1">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Expansion Phase (Month 13-24)</h3>
                <div className="space-y-2 text-sm text-blue-700">
                  <p>• International expansion</p>
                  <p>• Enterprise sales team buildout</p>
                  <p>• Additional language support</p>
                  <p>• Advanced features and integrations</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Target Market & Value Proposition */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Target Market</h2>
          <div className="space-y-6">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <h3 className="font-semibold text-blue-900">Primary Audience</h3>
              </div>
              <div className="space-y-3 text-sm text-blue-700">
                <div>
                  <h4 className="font-medium">Software Developers</h4>
                  <p>Individual developers seeking productivity tools</p>
                  <div className="mt-1 flex items-center">
                    <span className="text-xs text-blue-600 mr-2">Market Size:</span>
                    <span className="text-xs font-medium">2.8M potential users</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium">Development Teams</h4>
                  <p>Small to mid-size teams (5-20 developers)</p>
                  <div className="mt-1 flex items-center">
                    <span className="text-xs text-blue-600 mr-2">Market Size:</span>
                    <span className="text-xs font-medium">150K teams</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                  <Target className="w-5 h-5 text-white" />
                </div>
                <h3 className="font-semibold text-green-900">Secondary Audience</h3>
              </div>
              <div className="space-y-3 text-sm text-green-700">
                <div>
                  <h4 className="font-medium">Enterprise Organizations</h4>
                  <p>Large development organizations (50+ developers)</p>
                  <div className="mt-1 flex items-center">
                    <span className="text-xs text-green-600 mr-2">Market Size:</span>
                    <span className="text-xs font-medium">10K organizations</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium">Educational Institutions</h4>
                  <p>Universities and coding bootcamps</p>
                  <div className="mt-1 flex items-center">
                    <span className="text-xs text-green-600 mr-2">Market Size:</span>
                    <span className="text-xs font-medium">5K institutions</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h3 className="font-semibold text-purple-900 mb-2">Geographic Focus</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-purple-700">Initial Launch:</span>
                  <span className="font-medium text-purple-900">North America</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-purple-700">Phase 2 (Month 6):</span>
                  <span className="font-medium text-purple-900">Europe, Australia</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-purple-700">Phase 3 (Month 12):</span>
                  <span className="font-medium text-purple-900">Asia, Latin America</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Value Proposition</h2>
          <div className="space-y-6">
            <div className="p-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg">
              <h3 className="text-lg font-semibold mb-3">Core Value Proposition</h3>
              <p className="text-blue-100 text-lg mb-4">
                "CodeFlow AI reduces development time by 70% while maintaining enterprise-grade quality through 
                AI-powered full-cycle development automation."
              </p>
              <div className="grid grid-cols-3 gap-3 text-sm">
                <div className="bg-white/10 p-2 rounded">
                  <span className="font-medium">Speed</span>
                </div>
                <div className="bg-white/10 p-2 rounded">
                  <span className="font-medium">Quality</span>
                </div>
                <div className="bg-white/10 p-2 rounded">
                  <span className="font-medium">Automation</span>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">For Developers</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Eliminate repetitive coding tasks</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Focus on creative problem-solving</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Reduce debugging time by 60%</span>
                  </li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">For Team Leads</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Standardize code quality across team</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Improve project predictability</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Accelerate onboarding by 80%</span>
                  </li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">For CTOs</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Reduce development costs by 40%</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Accelerate time-to-market</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Enterprise security compliance</span>
                  </li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">For Business</h3>
                <ul className="space-y-2 text-sm text-gray-700">
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Faster innovation cycles</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Higher ROI on development</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>Competitive advantage</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Strategy */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Pricing Strategy</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-6 border border-gray-200 rounded-lg">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Free</h3>
              <div className="mt-2 text-2xl font-bold text-gray-900">$0</div>
              <div className="text-sm text-gray-500">Limited usage</div>
            </div>
            <ul className="space-y-3 text-sm">
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Basic code generation</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">5 languages supported</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">100 generations/month</span>
              </li>
              <li className="flex items-start space-x-2">
                <X className="w-4 h-4 text-gray-300 mt-0.5 flex-shrink-0" />
                <span className="text-gray-400">Advanced features</span>
              </li>
              <li className="flex items-start space-x-2">
                <X className="w-4 h-4 text-gray-300 mt-0.5 flex-shrink-0" />
                <span className="text-gray-400">Team collaboration</span>
              </li>
            </ul>
            <div className="mt-6">
              <button className="w-full py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                Sign Up Free
              </button>
            </div>
          </div>
          
          <div className="p-6 border-2 border-blue-500 rounded-lg relative">
            <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs font-bold px-3 py-1 rounded-bl-lg rounded-tr-lg">
              POPULAR
            </div>
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Pro</h3>
              <div className="mt-2 text-2xl font-bold text-gray-900">$29</div>
              <div className="text-sm text-gray-500">per user / month</div>
            </div>
            <ul className="space-y-3 text-sm">
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Advanced code generation</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">All languages supported</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Unlimited generations</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Automated testing</span>
              </li>
              <li className="flex items-start space-x-2">
                <X className="w-4 h-4 text-gray-300 mt-0.5 flex-shrink-0" />
                <span className="text-gray-400">Enterprise features</span>
              </li>
            </ul>
            <div className="mt-6">
              <button className="w-full py-2 px-4 bg-blue-600 rounded-md text-sm font-medium text-white hover:bg-blue-700">
                Start Pro Trial
              </button>
            </div>
          </div>
          
          <div className="p-6 border border-gray-200 rounded-lg">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Enterprise</h3>
              <div className="mt-2 text-2xl font-bold text-gray-900">$79</div>
              <div className="text-sm text-gray-500">per user / month</div>
            </div>
            <ul className="space-y-3 text-sm">
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Everything in Pro</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Team collaboration</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Advanced security</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Custom integrations</span>
              </li>
              <li className="flex items-start space-x-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">Dedicated support</span>
              </li>
            </ul>
            <div className="mt-6">
              <button className="w-full py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                Contact Sales
              </button>
            </div>
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Pricing Strategy Notes</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-blue-800 mb-1">Freemium Model</h4>
              <p className="text-blue-700">
                Free tier serves as acquisition channel and allows users to experience value before committing.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-1">Value-Based Pricing</h4>
              <p className="text-blue-700">
                Pricing reflects the significant time savings and productivity gains (70% reduction in dev time).
              </p>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-1">Annual Discount</h4>
              <p className="text-blue-700">
                20% discount for annual commitments to improve cash flow and reduce churn.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Marketing Plan */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Marketing Plan</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Channel Strategy</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={marketingChannels}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="channel" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [
                    name === 'roi' ? `${value}%` : `$${(value/1000).toFixed(0)}K`,
                    name === 'roi' ? 'ROI' : 'Investment'
                  ]} />
                  <Bar dataKey="cost" fill="#3B82F6" name="Investment" />
                  <Bar dataKey="roi" fill="#10B981" name="ROI" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-3">Developer Communities</h4>
              <p className="text-sm text-blue-700 mb-3">
                Engage with developers on GitHub, Stack Overflow, Reddit, and Discord communities.
              </p>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-blue-600">Reach:</span>
                  <span className="font-medium">500K devs</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600">Investment:</span>
                  <span className="font-medium">$25K</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600">ROI:</span>
                  <span className="font-medium">320%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600">Priority:</span>
                  <span className="font-medium">High</span>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-3">Content Marketing</h4>
              <p className="text-sm text-green-700 mb-3">
                Technical blog posts, tutorials, case studies, and educational content.
              </p>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-green-600">Reach:</span>
                  <span className="font-medium">200K devs</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600">Investment:</span>
                  <span className="font-medium">$15K</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600">ROI:</span>
                  <span className="font-medium">450%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600">Priority:</span>
                  <span className="font-medium">High</span>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h4 className="font-semibold text-orange-900 mb-3">Influencer Partnerships</h4>
              <p className="text-sm text-orange-700 mb-3">
                Collaborate with developer influencers, tech YouTubers, and industry experts.
              </p>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-orange-600">Reach:</span>
                  <span className="font-medium">150K devs</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-orange-600">Investment:</span>
                  <span className="font-medium">$35K</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-orange-600">ROI:</span>
                  <span className="font-medium">380%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-orange-600">Priority:</span>
                  <span className="font-medium">Medium</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sales Strategy & Growth Projection */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Sales Strategy</h2>
          <div className="space-y-6">
            <div className="p-4 border border-gray-200 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-3">Self-Service Acquisition</h3>
              <p className="text-sm text-gray-600 mb-3">
                Primary channel for individual developers and small teams.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Target Segment:</span>
                  <span className="font-medium">Individual & Small Teams</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Conversion Target:</span>
                  <span className="font-medium">5% free-to-paid</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">CAC Target:</span>
                  <span className="font-medium">$100</span>
                </div>
              </div>
            </div>
            
            <div className="p-4 border border-gray-200 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-3">Inside Sales</h3>
              <p className="text-sm text-gray-600 mb-3">
                For mid-market teams and organizations (10-100 developers).
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Target Segment:</span>
                  <span className="font-medium">Mid-Market Teams</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Sales Cycle:</span>
                  <span className="font-medium">30-45 days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Deal Size:</span>
                  <span className="font-medium">$10K-$50K ARR</span>
                </div>
              </div>
            </div>
            
            <div className="p-4 border border-gray-200 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-3">Enterprise Sales</h3>
              <p className="text-sm text-gray-600 mb-3">
                For large organizations (100+ developers) with complex needs.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Target Segment:</span>
                  <span className="font-medium">Enterprise</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Sales Cycle:</span>
                  <span className="font-medium">90-120 days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Deal Size:</span>
                  <span className="font-medium">$100K+ ARR</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Growth Projection</h2>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={growthProjection}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip formatter={(value, name) => [
                  name === 'users' ? value.toLocaleString() : `$${value.toLocaleString()}`,
                  name === 'users' ? 'Users' : 'Revenue'
                ]} />
                <Line yAxisId="left" type="monotone" dataKey="users" stroke="#3B82F6" name="Users" />
                <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#10B981" name="Revenue" />
              </LineChart>
            </ResponsiveContainer>
          </div>
          
          <div className="mt-6 grid grid-cols-2 gap-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-600">5,000</div>
              <div className="text-sm text-blue-700">Users (Year 1)</div>
            </div>
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-600">$180K</div>
              <div className="text-sm text-green-700">MRR (Year 1)</div>
            </div>
          </div>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Key Performance Indicators</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="w-5 h-5 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900">User Acquisition</h3>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Month 3:</span>
                <span className="font-medium">500 users</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Month 6:</span>
                <span className="font-medium">1,500 users</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Month 12:</span>
                <span className="font-medium">5,000 users</span>
              </div>
            </div>
          </div>
          
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Revenue</h3>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Month 3:</span>
                <span className="font-medium">$18K MRR</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Month 6:</span>
                <span className="font-medium">$54K MRR</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Month 12:</span>
                <span className="font-medium">$180K MRR</span>
              </div>
            </div>
          </div>
          
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Conversion</h3>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Free-to-Paid:</span>
                <span className="font-medium">5%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Trial-to-Paid:</span>
                <span className="font-medium">30%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Upsell Rate:</span>
                <span className="font-medium">15%</span>
              </div>
            </div>
          </div>
          
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900">Retention</h3>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Monthly Churn:</span>
                <span className="font-medium">3.5%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">NPS Target:</span>
                <span className="font-medium">50+</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">LTV/CAC Ratio:</span>
                <span className="font-medium">3:1</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Conclusion */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm p-8 text-white">
        <h2 className="text-2xl font-bold mb-4">Go-to-Market Summary</h2>
        <p className="text-blue-100 mb-6">
          CodeFlow AI's go-to-market strategy focuses on a developer-first approach with a freemium model 
          to drive adoption, followed by team and enterprise expansion. The strategy leverages the product's 
          unique value proposition of 70% time savings while maintaining enterprise quality.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-2">Key Success Factors</h3>
            <ul className="text-blue-100 text-sm space-y-1">
              <li>• Developer community engagement</li>
              <li>• Product-led growth motion</li>
              <li>• Clear value demonstration</li>
            </ul>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-2">Critical Milestones</h3>
            <ul className="text-blue-100 text-sm space-y-1">
              <li>• 500 beta users by Month 3</li>
              <li>• $50K MRR by Month 6</li>
              <li>• 5,000 users by Month 12</li>
            </ul>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-2">Next Steps</h3>
            <ul className="text-blue-100 text-sm space-y-1">
              <li>• Finalize beta program details</li>
              <li>• Develop content marketing plan</li>
              <li>• Establish community presence</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

function Check(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 6 9 17l-5-5" />
    </svg>
  );
}

function X(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M18 6 6 18" />
      <path d="m6 6 12 12" />
    </svg>
  );
}