import { useEffect, useState } from 'react';
import { TrendingUp, Users, DollarSign, Target, Star, Globe } from 'lucide-react';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { DatabaseService } from '../lib/supabase';

interface DashboardProps {
  selectedResearch?: any;
}

export function Dashboard({ selectedResearch }: DashboardProps) {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard data for the selected research
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if user is authenticated
        const currentUser = await DatabaseService.getCurrentUser();
        if (!currentUser) {
          setError('Please log in to view your dashboard data');
          setLoading(false);
          return;
        }

        // If no research is selected, show message
        if (!selectedResearch) {
          setError('Please select a market research to view dashboard data');
          setLoading(false);
          return;
        }

        console.log('Loading dashboard data for research:', selectedResearch.id);

        // Get the dashboard synthesis for the selected research
        const analyses = await DatabaseService.getResearchAnalysis(selectedResearch.id);
        const dashboardSynthesis = analyses.find(a => a.section === 'dashboard_synthesis');

        if (!dashboardSynthesis) {
          setError('Dashboard analysis not found for this research. The analysis may still be generating or may have failed.');
          setLoading(false);
          return;
        }

        console.log('Dashboard data loaded:', dashboardSynthesis.analysis_data);

        setDashboardData({
          research: selectedResearch,
          dashboard: dashboardSynthesis.analysis_data
        });
        
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        
        // Check if the error is due to authentication
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('User not authenticated')) {
          setError('Please log in to view your dashboard data');
        } else {
          setError('Failed to load dashboard data');
        }
      } finally {
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [selectedResearch]); // Re-run when selectedResearch changes

  // Prepare market size data for chart with defensive validation
  const getMarketSizeData = () => {
    if (!dashboardData) return [];
    
    try {
      // Defensive access with fallbacks at every level
      const marketOpportunity = dashboardData.dashboard?.marketOpportunity || {};
      const tam = marketOpportunity.tam || '$450B';
      const sam = marketOpportunity.sam || '$120B';
      const som = marketOpportunity.som || '$25B';
      
      // Extract numeric values (remove $ and B/M) with error handling
      const tamValue = parseFloat(tam.replace(/[^0-9.]/g, '')) || 450;
      const samValue = parseFloat(sam.replace(/[^0-9.]/g, '')) || 120;
      const somValue = parseFloat(som.replace(/[^0-9.]/g, '')) || 25;
      
      return [
        { name: 'TAM', value: tamValue, color: '#3B82F6' },
        { name: 'SAM', value: samValue, color: '#10B981' },
        { name: 'SOM', value: somValue, color: '#F97316' },
      ];
    } catch (error) {
      console.error('Error preparing market size data:', error);
      return [
        { name: 'TAM', value: 450, color: '#3B82F6' },
        { name: 'SAM', value: 120, color: '#10B981' },
        { name: 'SOM', value: 25, color: '#F97316' },
      ];
    }
  };

  // Prepare industry insights data for chart with defensive validation
  const getIndustryInsights = () => {
    if (!dashboardData) return [];
    
    try {
      // Safely access nested properties with defensive checks
      const marketOpportunity = dashboardData.dashboard?.marketOpportunity || {};
      const growthTrends = Array.isArray(marketOpportunity.growthTrends) ? 
        marketOpportunity.growthTrends : [];
      
      // Default fallback data if array is empty
      if (growthTrends.length === 0) {
        return [
          { name: 'Market Growth', growth: 20, market: 'N/A' },
          { name: 'Sector Trend', growth: 15, market: 'N/A' },
          { name: 'Industry Avg', growth: 10, market: 'N/A' }
        ];
      }
      
      // Transform into chart data with defensive parsing
      return growthTrends.map((trend: string, index: number) => {
        if (typeof trend !== 'string') {
          // Handle non-string items in array
          return {
            name: `Trend ${index + 1}`,
            growth: 10 + (index * 5),
            market: 'N/A'
          };
        }
        
        try {
          // Extract growth rate if available
          const growthMatch = trend.match(/(\d+)%/);
          const growth = growthMatch ? parseInt(growthMatch[1]) : 50 + (index * 10);
          
          // Extract market size if available
          const marketMatch = trend.match(/\$(\d+)([BM])/);
          const market = marketMatch ? `$${marketMatch[1]}${marketMatch[2]}` : 'N/A';
          
          // Extract name
          const nameParts = trend.split(' ');
          const name = nameParts.length > 2 ? 
            `${nameParts[0]} ${nameParts[1]}` : 
            trend.substring(0, Math.min(15, trend.length));
          
          return { name, growth, market };
        } catch (err) {
          // Return fallback for any parsing error
          return {
            name: `Trend ${index + 1}`,
            growth: 10 + (index * 5),
            market: 'N/A'
          };
        }
      });
    } catch (error) {
      console.error('Error preparing industry insights:', error);
      return [
        { name: 'Market Growth', growth: 20, market: 'N/A' },
        { name: 'Sector Trend', growth: 15, market: 'N/A' },
        { name: 'Industry Avg', growth: 10, market: 'N/A' }
      ];
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Loading your dashboard data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md">
          <div className="text-amber-500 mx-auto mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Dashboard Error</h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  // Add defensive check for dashboard data
  if (!dashboardData || !dashboardData.dashboard) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md">
          <div className="text-amber-500 mx-auto mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Dashboard Incomplete</h3>
          <p className="text-gray-600 mb-6">The market research synthesis data is incomplete or unavailable. Some analyses may still be in progress.</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const { dashboard } = dashboardData;
  const marketSizeData = getMarketSizeData();
  const industryInsights = getIndustryInsights();

  return (
    <div className="space-y-8">
      {/* Project Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="flex items-start justify-between">
          <div className="space-y-4">
            <h1 className="text-3xl font-bold text-gray-900">{dashboard.projectOverview.title}</h1>
            <p className="text-lg text-gray-600 max-w-3xl">
              {dashboard.projectOverview.description}
            </p>
          </div>
          <div className="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-lg">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-green-700 text-sm font-medium">{dashboard.projectOverview.projectType}</span>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Visibility Score</p>
              <p className="text-3xl font-bold text-blue-600">{dashboard.viabilityScore.score}/10</p>
              <p className="text-xs text-gray-500 mt-1">{dashboard.viabilityScore.score >= 8 ? 'Excellent potential' : 
                dashboard.viabilityScore.score >= 6 ? 'Good potential' : 'Moderate potential'}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Market (TAM)</p>
              <p className="text-3xl font-bold text-green-600">{dashboard.marketOpportunity.tam}</p>
              <p className="text-xs text-gray-500 mt-1">Global opportunity</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Globe className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Target Users</p>
              <p className="text-3xl font-bold text-orange-600">{dashboard.customerInsights.numberOfPersonas}</p>
              <p className="text-xs text-gray-500 mt-1">Customer Personas</p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Revenue Potential</p>
              <p className="text-3xl font-bold text-purple-600">{dashboard.financialHighlights.revenueProjections}</p>
              <p className="text-xs text-gray-500 mt-1">{dashboard.financialHighlights.profitabilityTimeline}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Market Size Analysis */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Market Size Analysis</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={marketSizeData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {marketSizeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => value} />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Total Addressable Market (TAM)</span>
              <span className="font-semibold">{dashboard.marketOpportunity.tam}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Serviceable Addressable Market (SAM)</span>
              <span className="font-semibold">{dashboard.marketOpportunity.sam}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Serviceable Obtainable Market (SOM)</span>
              <span className="font-semibold">{dashboard.marketOpportunity.som}</span>
            </div>
          </div>
        </div>

        {/* Industry Insights */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Industry Insights</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={industryInsights}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value: number) => `${value}% growth`} />
                <Bar dataKey="growth" fill="#3B82F6" radius={[4, 4, 0, 0]} name="Growth Rate" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 space-y-3">
            {dashboard.competitivePosition.insights && dashboard.competitivePosition.insights.map((insight: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">{insight.name}</span>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">{insight.growth}% growth</div>
                  <div className="text-xs text-gray-500">{insight.market} market</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Unique Selling Points */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Unique Selling Points</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {dashboard.uniqueSellingPoints.supportingUSPs.slice(0, 3).map((usp: string, index: number) => {
            const icons = [Target, TrendingUp, Users];
            const colors = ['blue', 'green', 'orange'];
            const Icon = icons[index % icons.length];
            const color = colors[index % colors.length];
            
            // Extract title and description
            const parts = usp.split(':');
            const title = parts[0].trim();
            const description = parts.length > 1 ? parts.slice(1).join(':').trim() : '';
            
            return (
              <div key={index} className={`text-center p-4 bg-${color}-50 rounded-lg`}>
                <div className={`w-12 h-12 bg-${color}-600 rounded-lg flex items-center justify-center mx-auto mb-3`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{title}</h4>
                <p className="text-sm text-gray-600">{description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}