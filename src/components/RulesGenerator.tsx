import React, { useState } from 'react';
import { FileText, Download, Copy, Code, Zap, Settings, Database, Shield, Palette, Globe, CheckCircle, ArrowRight, Sparkles } from 'lucide-react';

type ProjectType = 'web-app' | 'mobile-app' | 'api' | 'desktop-app' | 'ai-ml' | 'blockchain' | 'custom';
type TechStack = 'react-next' | 'vue-nuxt' | 'angular' | 'svelte' | 'vanilla' | 'react-native' | 'flutter' | 'node-express' | 'python-django' | 'python-flask' | 'java-spring' | 'dotnet' | 'go' | 'rust' | 'custom';

const projectTypes = [
  { id: 'web-app', name: 'Web Application', description: 'Frontend/Fullstack web applications', icon: Globe },
  { id: 'mobile-app', name: 'Mobile App', description: 'iOS/Android mobile applications', icon: Palette },
  { id: 'api', name: 'API/Backend', description: 'REST APIs and backend services', icon: Database },
  { id: 'desktop-app', name: 'Desktop App', description: 'Cross-platform desktop applications', icon: Settings },
  { id: 'ai-ml', name: 'AI/ML Project', description: 'Machine learning and AI applications', icon: Sparkles },
  { id: 'blockchain', name: 'Blockchain/Web3', description: 'Blockchain and decentralized applications', icon: Shield },
  { id: 'custom', name: 'Custom Project', description: 'Other types of projects', icon: Code }
];

const techStacks = {
  'web-app': [
    { id: 'react-next', name: 'React + Next.js', description: 'Modern React with Next.js framework' },
    { id: 'vue-nuxt', name: 'Vue + Nuxt.js', description: 'Vue.js with Nuxt.js framework' },
    { id: 'angular', name: 'Angular', description: 'Angular framework with TypeScript' },
    { id: 'svelte', name: 'Svelte/SvelteKit', description: 'Svelte framework for modern web apps' },
    { id: 'vanilla', name: 'Vanilla JS/HTML/CSS', description: 'Pure JavaScript without frameworks' }
  ],
  'mobile-app': [
    { id: 'react-native', name: 'React Native', description: 'Cross-platform mobile with React Native' },
    { id: 'flutter', name: 'Flutter', description: 'Cross-platform mobile with Flutter/Dart' }
  ],
  'api': [
    { id: 'node-express', name: 'Node.js + Express', description: 'JavaScript backend with Express' },
    { id: 'python-django', name: 'Python + Django', description: 'Python web framework Django' },
    { id: 'python-flask', name: 'Python + Flask', description: 'Lightweight Python web framework' },
    { id: 'java-spring', name: 'Java + Spring Boot', description: 'Enterprise Java with Spring Boot' },
    { id: 'dotnet', name: '.NET Core', description: 'Microsoft .NET Core framework' },
    { id: 'go', name: 'Go', description: 'Go programming language for APIs' },
    { id: 'rust', name: 'Rust', description: 'Systems programming with Rust' }
  ]
};

const ruleTemplates = {
  'react-next': `You are an expert in React, Next.js 14, TypeScript, TailwindCSS, and modern web development.

Code Style and Structure
- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Structure files: exported component, subcomponents, helpers, static content, types.

Naming Conventions
- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for components.

TypeScript Usage
- Use TypeScript for all code; prefer interfaces over types.
- Avoid enums; use maps or objects with 'as const' assertion.
- Use functional components with TypeScript interfaces.

Syntax and Formatting
- Use the "function" keyword for pure functions.
- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
- Use declarative JSX.

UI and Styling
- Use TailwindCSS for styling.
- Implement responsive design with Tailwind CSS.
- Use Lucide React for icons.

Performance Optimization
- Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC).
- Wrap client components in Suspense with fallback.
- Use dynamic loading for non-critical components.
- Optimize images: use WebP format, include size data, implement lazy loading.

Key Conventions
- Use 'nuqs' for URL search parameter state management.
- Optimize Web Vitals (LCP, CLS, FID).
- Limit 'use client':
  - Favor server components and Next.js SSR.
  - Use only for Web API access in small components.
  - Avoid for data fetching or state management.

Follow Next.js docs for Data Fetching, Rendering, and Routing.`,

  'vue-nuxt': `You are an expert in Vue.js 3, Nuxt.js 3, TypeScript, and modern web development.

Code Style and Structure
- Write clean, maintainable Vue 3 code using Composition API.
- Use TypeScript for type safety and better developer experience.
- Prefer composition over inheritance; use composables for shared logic.
- Structure components: template, script setup, style (scoped).

Vue 3 Best Practices
- Use Composition API with <script setup> syntax.
- Leverage reactivity with ref(), reactive(), and computed().
- Use provide/inject for dependency injection.
- Implement proper component lifecycle with onMounted, onUnmounted, etc.

Nuxt.js Conventions
- Follow Nuxt 3 directory structure and auto-imports.
- Use server-side rendering (SSR) and static site generation (SSG) appropriately.
- Leverage Nuxt modules and plugins for extended functionality.
- Use Nuxt's built-in state management with useState().

TypeScript Integration
- Define proper interfaces for props, emits, and data structures.
- Use generic types for reusable components.
- Leverage Vue's built-in TypeScript support.

Styling and UI
- Use CSS modules or scoped styles.
- Implement responsive design principles.
- Consider using UI libraries like Vuetify or PrimeVue.

Performance
- Optimize bundle size with tree-shaking and code splitting.
- Use lazy loading for routes and components.
- Implement proper caching strategies.

Follow Vue.js and Nuxt.js official documentation for best practices.`,

  'python-django': `You are an expert in Python, Django, Django REST Framework, and web development.

Code Style and Structure
- Write clean, Pythonic code following PEP 8 guidelines.
- Use Django's MVT (Model-View-Template) pattern effectively.
- Organize code into reusable apps with clear separation of concerns.
- Use type hints for better code documentation and IDE support.

Django Best Practices
- Follow Django's "Don't Repeat Yourself" (DRY) principle.
- Use Django's built-in features: ORM, admin, authentication, etc.
- Implement proper URL routing with meaningful names.
- Use Django's template system with template inheritance.

Models and Database
- Design efficient database schemas with proper relationships.
- Use Django ORM effectively with select_related and prefetch_related.
- Implement proper model validation and custom managers.
- Use migrations for database schema changes.

Views and APIs
- Use class-based views for complex logic, function-based for simple cases.
- Implement proper error handling and validation.
- Use Django REST Framework for API development.
- Follow RESTful API design principles.

Security
- Implement Django's security features: CSRF protection, SQL injection prevention.
- Use Django's authentication and authorization system.
- Validate and sanitize user input.
- Follow OWASP security guidelines.

Testing and Deployment
- Write comprehensive tests using Django's testing framework.
- Use fixtures and factories for test data.
- Implement proper logging and monitoring.
- Follow Django deployment best practices.

Follow Django documentation and community best practices.`,

  'react-native': `You are an expert in React Native, Expo, TypeScript, and mobile app development.

Code Style and Structure
- Write clean, performant React Native code with TypeScript.
- Use functional components with hooks.
- Implement proper component composition and reusability.
- Follow React Native's platform-specific guidelines.

React Native Best Practices
- Use Expo for rapid development and easy deployment.
- Implement proper navigation with React Navigation.
- Use native modules when necessary for platform-specific features.
- Optimize performance with FlatList, VirtualizedList for large datasets.

Styling and UI
- Use StyleSheet for component styling.
- Implement responsive design for different screen sizes.
- Use platform-specific styles when needed (Platform.OS).
- Consider using UI libraries like NativeBase or React Native Elements.

State Management
- Use React hooks for local state management.
- Implement global state with Context API or Redux Toolkit.
- Use async storage for data persistence.

Performance Optimization
- Optimize images and assets for mobile.
- Implement lazy loading and code splitting.
- Use React.memo and useMemo for expensive computations.
- Profile and optimize using Flipper or React DevTools.

Platform Integration
- Handle platform permissions properly.
- Implement proper error boundaries and crash reporting.
- Use native device features: camera, GPS, push notifications.
- Test on both iOS and Android platforms.

Follow React Native and Expo documentation for best practices.`,

  'node-express': `You are an expert in Node.js, Express.js, TypeScript, and backend development.

Code Style and Structure
- Write clean, maintainable Node.js code with TypeScript.
- Use async/await for asynchronous operations.
- Implement proper error handling with try-catch blocks.
- Structure code with clear separation of concerns: routes, controllers, services, models.

Express.js Best Practices
- Use middleware effectively for cross-cutting concerns.
- Implement proper routing with Express Router.
- Use environment variables for configuration.
- Implement request validation and sanitization.

Database Integration
- Use ORM/ODM like Prisma, TypeORM, or Mongoose.
- Implement proper database connection pooling.
- Use transactions for data consistency.
- Optimize database queries and implement caching.

API Development
- Follow RESTful API design principles.
- Implement proper HTTP status codes and error responses.
- Use API versioning strategies.
- Document APIs with tools like Swagger/OpenAPI.

Security
- Implement authentication and authorization (JWT, OAuth).
- Use HTTPS and security headers.
- Validate and sanitize all inputs.
- Implement rate limiting and CORS properly.

Testing and Monitoring
- Write unit and integration tests with Jest or Mocha.
- Implement proper logging with Winston or similar.
- Use monitoring tools for performance tracking.
- Implement health checks and graceful shutdowns.

Follow Node.js and Express.js best practices and security guidelines.`
};

export function RulesGenerator() {
  const [selectedProjectType, setSelectedProjectType] = useState<ProjectType | null>(null);
  const [selectedTechStack, setSelectedTechStack] = useState<TechStack | null>(null);
  const [projectDescription, setProjectDescription] = useState('');
  const [additionalRequirements, setAdditionalRequirements] = useState('');
  const [generatedRules, setGeneratedRules] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleGenerateRules = () => {
    if (!selectedTechStack) return;
    
    setIsGenerating(true);
    
    // Simulate AI generation process
    setTimeout(() => {
      let baseRules = ruleTemplates[selectedTechStack] || ruleTemplates['react-next'];
      
      // Customize rules based on project description and requirements
      if (projectDescription) {
        baseRules += `\n\nProject Context\n- Project: ${projectDescription}`;
      }
      
      if (additionalRequirements) {
        baseRules += `\n\nAdditional Requirements\n${additionalRequirements}`;
      }
      
      baseRules += `\n\nGeneral Guidelines
- Always prioritize code readability and maintainability.
- Follow industry best practices and security guidelines.
- Write comprehensive comments for complex logic.
- Implement proper error handling and logging.
- Consider performance implications in all decisions.
- Use version control best practices with meaningful commit messages.`;

      setGeneratedRules(baseRules);
      setIsGenerating(false);
    }, 2000);
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedRules);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleDownloadRules = () => {
    const blob = new Blob([generatedRules], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '.cursorrules';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const availableTechStacks = selectedProjectType && techStacks[selectedProjectType] 
    ? techStacks[selectedProjectType] 
    : [];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Rules Generator para Cursor/Windsurf</h1>
        <p className="text-gray-600">
          Genera archivos de reglas personalizados para IDEs asistidos por IA como Cursor o Windsurf. 
          Estas reglas instruyen al asistente de IA sobre cómo actuar, qué tecnologías usar y qué 
          mejores prácticas seguir en tu proyecto específico.
        </p>
      </div>

      {!generatedRules ? (
        <>
          {/* Project Type Selection */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">Tipo de Proyecto</h2>
              <p className="text-gray-600 mt-1">Selecciona el tipo de proyecto que vas a desarrollar</p>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {projectTypes.map((type) => {
                  const Icon = type.icon;
                  const isSelected = selectedProjectType === type.id;
                  
                  return (
                    <div
                      key={type.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50 shadow-sm'
                          : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedProjectType(type.id as ProjectType)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          isSelected ? 'bg-blue-600' : 'bg-gray-100'
                        }`}>
                          <Icon className={`w-5 h-5 ${isSelected ? 'text-white' : 'text-gray-600'}`} />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 mb-1">{type.name}</h3>
                          <p className="text-sm text-gray-600">{type.description}</p>
                        </div>
                        {isSelected && <CheckCircle className="w-5 h-5 text-blue-600" />}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Tech Stack Selection */}
          {selectedProjectType && availableTechStacks.length > 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-semibold text-gray-900">Stack Tecnológico</h2>
                <p className="text-gray-600 mt-1">Elige las tecnologías principales de tu proyecto</p>
              </div>
              
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableTechStacks.map((stack) => {
                    const isSelected = selectedTechStack === stack.id;
                    
                    return (
                      <div
                        key={stack.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 shadow-sm'
                            : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                        }`}
                        onClick={() => setSelectedTechStack(stack.id as TechStack)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium text-gray-900 mb-1">{stack.name}</h3>
                            <p className="text-sm text-gray-600">{stack.description}</p>
                          </div>
                          {isSelected && <CheckCircle className="w-5 h-5 text-blue-600" />}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Project Details */}
          {selectedTechStack && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-semibold text-gray-900">Detalles del Proyecto</h2>
                <p className="text-gray-600 mt-1">Proporciona información adicional para personalizar las reglas</p>
              </div>
              
              <div className="p-6 space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Descripción del Proyecto
                  </label>
                  <textarea
                    value={projectDescription}
                    onChange={(e) => setProjectDescription(e.target.value)}
                    className="w-full h-24 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe brevemente tu proyecto, su propósito y características principales..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Requisitos Adicionales (Opcional)
                  </label>
                  <textarea
                    value={additionalRequirements}
                    onChange={(e) => setAdditionalRequirements(e.target.value)}
                    className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Especifica cualquier requisito adicional, patrones de diseño específicos, librerías que prefieres usar, estándares de código, etc..."
                  />
                </div>
                
                <div className="flex justify-center">
                  <button
                    onClick={handleGenerateRules}
                    disabled={isGenerating}
                    className={`px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center ${
                      isGenerating ? 'opacity-75 cursor-not-allowed' : ''
                    }`}
                  >
                    {isGenerating ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Generando Reglas...
                      </>
                    ) : (
                      <>
                        <Zap className="w-5 h-5 mr-2" />
                        Generar Reglas con IA
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        /* Generated Rules Display */
        <div className="space-y-6">
          {/* Success Message */}
          <div className="bg-green-50 border border-green-200 rounded-xl p-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-green-900">¡Reglas Generadas Exitosamente!</h3>
                <p className="text-green-700">
                  Tu archivo de reglas personalizado está listo para usar en Cursor o Windsurf.
                </p>
              </div>
            </div>
          </div>

          {/* Rules Display */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">.cursorrules</h2>
                  <p className="text-gray-600">Archivo de reglas para tu proyecto</p>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleCopyToClipboard}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    {copied ? 'Copiado!' : 'Copiar'}
                  </button>
                  <button
                    onClick={handleDownloadRules}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Descargar
                  </button>
                  <button
                    onClick={() => {
                      setGeneratedRules('');
                      setSelectedProjectType(null);
                      setSelectedTechStack(null);
                      setProjectDescription('');
                      setAdditionalRequirements('');
                    }}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                  >
                    Generar Nuevo
                  </button>
                </div>
              </div>
            </div>
            
            <div className="p-6">
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre className="text-green-400 text-sm font-mono whitespace-pre-wrap">
                  {generatedRules}
                </pre>
              </div>
            </div>
          </div>

          {/* Usage Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">Cómo usar este archivo</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                  1
                </div>
                <div>
                  <h4 className="font-medium text-blue-900">Para Cursor</h4>
                  <p className="text-sm text-blue-700">
                    Guarda el archivo como <code className="bg-blue-100 px-1 rounded">.cursorrules</code> en la raíz de tu proyecto.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                  2
                </div>
                <div>
                  <h4 className="font-medium text-blue-900">Para Windsurf</h4>
                  <p className="text-sm text-blue-700">
                    Guarda el archivo como <code className="bg-blue-100 px-1 rounded">.windsurfrules</code> en la raíz de tu proyecto.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mt-0.5">
                  3
                </div>
                <div>
                  <h4 className="font-medium text-blue-900">Activación</h4>
                  <p className="text-sm text-blue-700">
                    El asistente de IA automáticamente detectará y aplicará estas reglas cuando trabajes en tu proyecto.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}