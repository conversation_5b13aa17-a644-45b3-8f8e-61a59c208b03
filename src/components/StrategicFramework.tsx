import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Shield, AlertTriangle, TrendingUp, Zap, Loader2 } from 'lucide-react';
import { DatabaseService } from '../lib/supabase';

const sections = [
  { id: 'intro', name: 'Introduction', icon: Shield },
  { id: 'strategy', name: 'Strategy', icon: TrendingUp },
  { id: 'swot', name: 'SWOT Analysis', icon: Shield },
  { id: 'pestel', name: 'PESTEL Analysis', icon: AlertTriangle },
  { id: 'porter', name: 'Porter\'s Five Forces', icon: Zap },
  { id: 'catwoe', name: 'CATWOE Analysis', icon: TrendingUp },
  { id: 'gamechanging', name: 'Game Changing Idea', icon: Zap },
];

interface StrategicFrameworkProps {
  selectedResearch?: any;
}

export function StrategicFramework({ selectedResearch }: StrategicFrameworkProps) {
  const [expandedSection, setExpandedSection] = useState('intro');
  const [strategicData, setStrategicData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load strategic framework data
  useEffect(() => {
    const loadStrategicData = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!selectedResearch) {
          setError('Please select a research to view strategic framework data');
          setLoading(false);
          return;
        }

        console.log('Loading strategic data for research:', selectedResearch.id);

        // Get strategic framework analyses
        const analyses = await DatabaseService.getResearchAnalysis(selectedResearch.id);
        const strategicAnalyses = analyses.filter(a => a.section.startsWith('strategic_'));

        if (strategicAnalyses.length === 0) {
          setError('Strategic framework analysis not found for this research');
          setLoading(false);
          return;
        }

        // Organize data by section
        const organizedData: any = {};
        strategicAnalyses.forEach(analysis => {
          const sectionName = analysis.section.replace('strategic_', '');
          organizedData[sectionName] = analysis.analysis_data;
        });

        console.log('Strategic data loaded:', organizedData);
        setStrategicData(organizedData);

      } catch (error) {
        console.error('Error loading strategic data:', error);
        setError('Failed to load strategic framework data');
      } finally {
        setLoading(false);
      }
    };

    loadStrategicData();
  }, [selectedResearch]);

  const toggleSection = (sectionId: string) => {
    setExpandedSection(expandedSection === sectionId ? '' : sectionId);
  };

  // Helper function to get data from strategicData or return default
  const getDataOrDefault = (key: string, defaultValue: any) => {
    return strategicData && strategicData[key] ? strategicData[key] : defaultValue;
  };

  // Helper function to render section content with beautiful structure
  const renderSectionContent = (sectionId: string) => {
    const data = strategicData && strategicData[sectionId] ? strategicData[sectionId] : null;

    switch (sectionId) {
      case 'intro':
        return (
          <div className="space-y-4">
            <p className="text-gray-700">
              {data?.overview || data?.introduction ||
                `${selectedResearch?.title || 'This project'} represents a transformational opportunity in its target market.
                This analysis provides comprehensive strategic insights to guide decision-making and market positioning.`}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">Strategic Vision</h4>
                <p className="text-blue-700 text-sm">
                  {data?.strategic_vision || data?.vision ||
                    'Establish market leadership through innovative solutions and strategic positioning.'}
                </p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-900 mb-2">Market Positioning</h4>
                <p className="text-green-700 text-sm">
                  {data?.market_positioning || data?.positioning ||
                    'Differentiated positioning focused on unique value proposition and competitive advantages.'}
                </p>
              </div>
            </div>
          </div>
        );

      case 'strategy':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Business Strategy</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {data?.business_strategy ?
                    (Array.isArray(data.business_strategy) ? data.business_strategy : [data.business_strategy]).map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    )) :
                    <>
                      <li>• Market-driven approach</li>
                      <li>• Customer-centric focus</li>
                      <li>• Scalable business model</li>
                      <li>• Strategic partnerships</li>
                    </>
                  }
                </ul>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Business Framework</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {data?.business_framework ?
                    (Array.isArray(data.business_framework) ? data.business_framework : [data.business_framework]).map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    )) :
                    <>
                      <li>• Scalable platform architecture</li>
                      <li>• API-first approach</li>
                      <li>• Secure multi-tenant design</li>
                      <li>• Extensible ecosystem</li>
                    </>
                  }
                </ul>
              </div>
            </div>

            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Key Requirements</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {data?.requirements ?
                    (Array.isArray(data.requirements) ? data.requirements : [data.requirements]).map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    )) :
                    <>
                      <li>• High performance standards</li>
                      <li>• Reliability and uptime</li>
                      <li>• Security compliance</li>
                      <li>• Scalability requirements</li>
                    </>
                  }
                </ul>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Revenue Streams</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {data?.revenue_streams ?
                    (Array.isArray(data.revenue_streams) ? data.revenue_streams : [data.revenue_streams]).map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    )) :
                    <>
                      <li>• Subscription-based model</li>
                      <li>• Enterprise licensing</li>
                      <li>• Professional services</li>
                      <li>• Partnership revenue</li>
                    </>
                  }
                </ul>
              </div>
            </div>
          </div>
        );

      default:
        // For other sections (SWOT, PESTEL, Porter, CATWOE, Game Changing), render as structured data
        if (!data) {
          return (
            <div className="text-gray-500 italic">
              No data available for this section. The analysis may still be generating or may have failed.
            </div>
          );
        }

        if (typeof data === 'string') {
          return <div className="text-gray-700 whitespace-pre-wrap">{data}</div>;
        }

        if (typeof data === 'object' && data !== null) {
          return (
            <div className="space-y-4">
              {Object.entries(data).map(([key, value]) => (
                <div key={key} className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2 capitalize">
                    {key.replace(/_/g, ' ')}
                  </h4>
                  <div className="text-gray-700">
                    {typeof value === 'string' ? (
                      <div className="whitespace-pre-wrap">{value}</div>
                    ) : Array.isArray(value) ? (
                      <ul className="list-disc list-inside space-y-1">
                        {value.map((item, index) => (
                          <li key={index}>{typeof item === 'string' ? item : JSON.stringify(item)}</li>
                        ))}
                      </ul>
                    ) : (
                      <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto">
                        {JSON.stringify(value, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>
              ))}
            </div>
          );
        }

        return (
          <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        );
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading strategic framework data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-gray-600 mb-6">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Strategic Framework</h1>
        <p className="text-gray-600">
          {selectedResearch ?
            `Comprehensive strategic analysis for ${selectedResearch.title} including business strategy, market positioning, competitive analysis, and strategic frameworks to guide decision-making.` :
            'Comprehensive strategic analysis including business strategy, market positioning, competitive analysis, and strategic frameworks to guide decision-making.'
          }
        </p>
      </div>

      {/* Dynamic Sections */}
      {sections.map((section) => {
        const Icon = section.icon;
        return (
          <div key={section.id} className="bg-white rounded-xl shadow-sm border border-gray-100">
            <button
              onClick={() => toggleSection(section.id)}
              className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <Icon className="w-5 h-5 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">{section.name}</h2>
              </div>
              {expandedSection === section.id ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
            </button>

            {expandedSection === section.id && (
              <div className="px-6 pb-6 border-t">
                <div className="pt-6 space-y-4">
                  {renderSectionContent(section.id)}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}