import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, Shield, AlertTriangle, TrendingUp, Zap, Loader2 } from 'lucide-react';
import { DatabaseService } from '../lib/supabase';

const sections = [
  { id: 'intro', name: 'Introduction', icon: Shield },
  { id: 'strategy', name: 'Strategy', icon: TrendingUp },
  { id: 'swot', name: 'SWOT Analysis', icon: Shield },
  { id: 'pestel', name: 'PESTEL Analysis', icon: AlertTriangle },
  { id: 'porter', name: 'Porter\'s Five Forces', icon: Zap },
  { id: 'catwoe', name: 'CATWOE Analysis', icon: TrendingUp },
  { id: 'gamechanging', name: 'Game Changing Idea', icon: Zap },
];

interface StrategicFrameworkProps {
  selectedResearch?: any;
}

export function StrategicFramework({ selectedResearch }: StrategicFrameworkProps) {
  const [expandedSection, setExpandedSection] = useState('intro');
  const [strategicData, setStrategicData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load strategic framework data
  useEffect(() => {
    const loadStrategicData = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!selectedResearch) {
          setError('Please select a research to view strategic framework data');
          setLoading(false);
          return;
        }

        console.log('Loading strategic data for research:', selectedResearch.id);

        // Get strategic framework analyses
        const analyses = await DatabaseService.getResearchAnalysis(selectedResearch.id);
        const strategicAnalyses = analyses.filter(a => a.section.startsWith('strategic_'));

        if (strategicAnalyses.length === 0) {
          setError('Strategic framework analysis not found for this research');
          setLoading(false);
          return;
        }

        // Organize data by section
        const organizedData: any = {};
        strategicAnalyses.forEach(analysis => {
          const sectionName = analysis.section.replace('strategic_', '');
          organizedData[sectionName] = analysis.analysis_data;
        });

        console.log('Strategic data loaded:', organizedData);
        setStrategicData(organizedData);

      } catch (error) {
        console.error('Error loading strategic data:', error);
        setError('Failed to load strategic framework data');
      } finally {
        setLoading(false);
      }
    };

    loadStrategicData();
  }, [selectedResearch]);

  const toggleSection = (sectionId: string) => {
    setExpandedSection(expandedSection === sectionId ? '' : sectionId);
  };

  // Helper function to get data from strategicData or return default
  const getDataOrDefault = (key: string, defaultValue: any) => {
    return strategicData && strategicData[key] ? strategicData[key] : defaultValue;
  };

  // Helper function to render section content with beautiful structure
  const renderSectionContent = (sectionId: string) => {
    const data = strategicData && strategicData[sectionId] ? strategicData[sectionId] : null;

    switch (sectionId) {
      case 'intro':
        return (
          <div className="space-y-4">
            <p className="text-gray-700">
              {data?.overview || data?.introduction ||
                `${selectedResearch?.title || 'This project'} represents a transformational opportunity in its target market.
                This analysis provides comprehensive strategic insights to guide decision-making and market positioning.`}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">Strategic Vision</h4>
                <p className="text-blue-700 text-sm">
                  {data?.strategic_vision || data?.vision ||
                    'Establish market leadership through innovative solutions and strategic positioning.'}
                </p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-900 mb-2">Market Positioning</h4>
                <p className="text-green-700 text-sm">
                  {data?.market_positioning || data?.positioning ||
                    'Differentiated positioning focused on unique value proposition and competitive advantages.'}
                </p>
              </div>
            </div>
          </div>
        );

      case 'strategy':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Business Strategy</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {data?.business_strategy ?
                    (Array.isArray(data.business_strategy) ? data.business_strategy : [data.business_strategy]).map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    )) :
                    <>
                      <li>• Market-driven approach</li>
                      <li>• Customer-centric focus</li>
                      <li>• Scalable business model</li>
                      <li>• Strategic partnerships</li>
                    </>
                  }
                </ul>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Business Framework</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {data?.business_framework ?
                    (Array.isArray(data.business_framework) ? data.business_framework : [data.business_framework]).map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    )) :
                    <>
                      <li>• Scalable platform architecture</li>
                      <li>• API-first approach</li>
                      <li>• Secure multi-tenant design</li>
                      <li>• Extensible ecosystem</li>
                    </>
                  }
                </ul>
              </div>
            </div>

            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Key Requirements</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {data?.requirements ?
                    (Array.isArray(data.requirements) ? data.requirements : [data.requirements]).map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    )) :
                    <>
                      <li>• High performance standards</li>
                      <li>• Reliability and uptime</li>
                      <li>• Security compliance</li>
                      <li>• Scalability requirements</li>
                    </>
                  }
                </ul>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Revenue Streams</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {data?.revenue_streams ?
                    (Array.isArray(data.revenue_streams) ? data.revenue_streams : [data.revenue_streams]).map((item: string, index: number) => (
                      <li key={index}>• {item}</li>
                    )) :
                    <>
                      <li>• Subscription-based model</li>
                      <li>• Enterprise licensing</li>
                      <li>• Professional services</li>
                      <li>• Partnership revenue</li>
                    </>
                  }
                </ul>
              </div>
            </div>
          </div>
        );

      default:
        // For other sections (SWOT, PESTEL, Porter, CATWOE, Game Changing), render as structured data
        if (!data) {
          return (
            <div className="text-gray-500 italic">
              No data available for this section. The analysis may still be generating or may have failed.
            </div>
          );
        }

        if (typeof data === 'string') {
          return <div className="text-gray-700 whitespace-pre-wrap">{data}</div>;
        }

        if (typeof data === 'object' && data !== null) {
          return (
            <div className="space-y-4">
              {Object.entries(data).map(([key, value]) => (
                <div key={key} className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2 capitalize">
                    {key.replace(/_/g, ' ')}
                  </h4>
                  <div className="text-gray-700">
                    {typeof value === 'string' ? (
                      <div className="whitespace-pre-wrap">{value}</div>
                    ) : Array.isArray(value) ? (
                      <ul className="list-disc list-inside space-y-1">
                        {value.map((item, index) => (
                          <li key={index}>{typeof item === 'string' ? item : JSON.stringify(item)}</li>
                        ))}
                      </ul>
                    ) : (
                      <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto">
                        {JSON.stringify(value, null, 2)}
                      </pre>
                    )}
                  </div>
                </div>
              ))}
            </div>
          );
        }

        return (
          <pre className="bg-gray-50 p-3 rounded text-sm overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        );
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading strategic framework data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-gray-600 mb-6">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Strategic Framework</h1>
        <p className="text-gray-600">
          {selectedResearch ?
            `Comprehensive strategic analysis for ${selectedResearch.title} including business strategy, market positioning, competitive analysis, and strategic frameworks to guide decision-making.` :
            'Comprehensive strategic analysis including business strategy, market positioning, competitive analysis, and strategic frameworks to guide decision-making.'
          }
        </p>
      </div>

      {/* Dynamic Sections */}
      {sections.map((section) => {
        const Icon = section.icon;
        return (
          <div key={section.id} className="bg-white rounded-xl shadow-sm border border-gray-100">
            <button
              onClick={() => toggleSection(section.id)}
              className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <Icon className="w-5 h-5 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">{section.name}</h2>
              </div>
              {expandedSection === section.id ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
            </button>

            {expandedSection === section.id && (
              <div className="px-6 pb-6 border-t">
                <div className="pt-6 space-y-4">
                  {renderSectionContent(section.id)}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <button
          onClick={() => toggleSection('swot')}
          className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">SWOT Analysis</h2>
          </div>
          {expandedSection === 'swot' ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
        </button>
        
        {expandedSection === 'swot' && (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-3 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Strengths
                  </h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Tecnología AI avanzada y propietaria</li>
                    <li>• Equipo con experiencia en ML/AI</li>
                    <li>• Partnerships estratégicos establecidos</li>
                    <li>• Arquitectura escalable y segura</li>
                    <li>• Time-to-market ventajoso</li>
                  </ul>
                </div>
                
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-3 flex items-center">
                    <Zap className="w-4 h-4 mr-2" />
                    Opportunities
                  </h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Crecimiento del mercado AI/ML (85% anual)</li>
                    <li>• Adopción acelerada de low-code/no-code</li>
                    <li>• Demanda de automatización en desarrollo</li>
                    <li>• Expansión a mercados emergentes</li>
                    <li>• Integración con cloud platforms</li>
                  </ul>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="font-semibold text-red-900 mb-3 flex items-center">
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Weaknesses
                  </h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• Dependencia de talento especializado</li>
                    <li>• Altos costos de infraestructura AI</li>
                    <li>• Curva de aprendizaje para usuarios</li>
                    <li>• Limitaciones en legacy systems</li>
                    <li>• Necesidad de capital significativo</li>
                  </ul>
                </div>
                
                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <h4 className="font-semibold text-orange-900 mb-3 flex items-center">
                    <Shield className="w-4 h-4 mr-2" />
                    Threats
                  </h4>
                  <ul className="text-sm text-orange-700 space-y-1">
                    <li>• Competencia de tech giants (Microsoft, Google)</li>
                    <li>• Cambios regulatorios en AI</li>
                    <li>• Preocupaciones de seguridad y privacidad</li>
                    <li>• Saturación del mercado de dev tools</li>
                    <li>• Dependencia de APIs de terceros</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* PESTEL Analysis */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <button
          onClick={() => toggleSection('pestel')}
          className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-5 h-5 text-orange-600" />
            <h2 className="text-xl font-semibold text-gray-900">PESTEL Analysis</h2>
          </div>
          {expandedSection === 'pestel' ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
        </button>
        
        {expandedSection === 'pestel' && (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Political</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Regulaciones AI en desarrollo</li>
                  <li>• Políticas de data sovereignty</li>
                  <li>• Incentivos para tech startups</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Economic</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Crecimiento del sector tech</li>
                  <li>• Inversión en transformación digital</li>
                  <li>• Volatilidad en funding</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Social</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Adopción de AI por developers</li>
                  <li>• Preocupaciones sobre job displacement</li>
                  <li>• Remote work normalization</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Technological</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Avances en LLMs y code generation</li>
                  <li>• Cloud computing evolution</li>
                  <li>• Edge computing adoption</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Environmental</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Presión por green computing</li>
                  <li>• Eficiencia energética en AI</li>
                  <li>• Sustainability reporting</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Legal</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• GDPR y data protection</li>
                  <li>• IP y copyright en AI-generated code</li>
                  <li>• Liability frameworks</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Porter's Five Forces */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <button
          onClick={() => toggleSection('porter')}
          className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <Zap className="w-5 h-5 text-purple-600" />
            <h2 className="text-xl font-semibold text-gray-900">Porter's Five Forces Analysis</h2>
          </div>
          {expandedSection === 'porter' ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
        </button>
        
        {expandedSection === 'porter' && (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">Threat of New Entrants</h4>
                    <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded">HIGH</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Barreras moderadas debido a la complejidad técnica, pero el crecimiento del mercado atrae nuevos competidores.
                  </p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    <li>• Capital intensivo para AI infrastructure</li>
                    <li>• Necesidad de talento especializado</li>
                    <li>• Network effects débiles inicialmente</li>
                  </ul>
                </div>

                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">Bargaining Power of Suppliers</h4>
                    <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">HIGH</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Dependencia de pocos proveedores de AI/ML infrastructure y talent pool limitado.
                  </p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    <li>• Cloud providers dominan el mercado</li>
                    <li>• Talent pool limitado en AI/ML</li>
                    <li>• APIs de terceros críticas</li>
                  </ul>
                </div>

                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">Bargaining Power of Buyers</h4>
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">MEDIUM</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Developers tienen opciones pero switching costs están creciendo con integración profunda.
                  </p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    <li>• Múltiples alternativas disponibles</li>
                    <li>• Switching costs moderados</li>
                    <li>• Price sensitivity variable</li>
                  </ul>
                </div>

                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">Threat of Substitutes</h4>
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">LOW</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Pocas alternativas ofrecen el mismo nivel de automatización y calidad de código.
                  </p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    <li>• Traditional dev tools menos eficientes</li>
                    <li>• Manual coding no escalable</li>
                    <li>• Low-code platforms limitadas</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-purple-900">Competitive Rivalry</h4>
                  <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">HIGH</span>
                </div>
                <p className="text-sm text-purple-700 mb-3">
                  Competencia intensa con jugadores establecidos y nuevos entrantes invirtiendo fuertemente en AI-powered development tools.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                  <div>
                    <strong className="text-purple-900">Major Players:</strong>
                    <ul className="text-purple-700 mt-1 space-y-1">
                      <li>• GitHub Copilot</li>
                      <li>• Tabnine</li>
                      <li>• Replit</li>
                    </ul>
                  </div>
                  <div>
                    <strong className="text-purple-900">Competitive Factors:</strong>
                    <ul className="text-purple-700 mt-1 space-y-1">
                      <li>• Innovation speed</li>
                      <li>• Integration capabilities</li>
                      <li>• Pricing strategies</li>
                    </ul>
                  </div>
                  <div>
                    <strong className="text-purple-900">Differentiation:</strong>
                    <ul className="text-purple-700 mt-1 space-y-1">
                      <li>• Quality focus</li>
                      <li>• Enterprise features</li>
                      <li>• Specialized workflows</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Game Changing Idea */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm p-8 text-white">
        <div className="flex items-center space-x-3 mb-6">
          <Zap className="w-6 h-6" />
          <h2 className="text-2xl font-bold">Game Changing Idea</h2>
        </div>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3">The Core Idea</h3>
            <p className="text-blue-100 text-lg leading-relaxed">
              CodeFlow AI no es solo otra herramienta de code generation. Es la primera plataforma que combina 
              <strong className="text-white"> AI contextual profunda</strong>, 
              <strong className="text-white"> testing automático inteligente</strong>, y 
              <strong className="text-white"> deployment predictivo</strong> en un workflow unificado que aprende 
              continuamente de cada proyecto para optimizar no solo el código, sino todo el ciclo de desarrollo.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <h4 className="font-semibold mb-2">🧠 Contextual AI</h4>
              <p className="text-sm text-blue-100">
                Entiende el contexto completo del proyecto, arquitectura y patrones para generar código que se integra perfectamente.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <h4 className="font-semibold mb-2">🔄 Continuous Learning</h4>
              <p className="text-sm text-blue-100">
                Cada interacción mejora el modelo, adaptándose al estilo del equipo y mejorando la calidad progresivamente.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <h4 className="font-semibold mb-2">🚀 Predictive Deployment</h4>
              <p className="text-sm text-blue-100">
                Predice y previene issues en producción antes del deployment, garantizando estabilidad enterprise.
              </p>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h4 className="font-semibold text-lg mb-3">Market Impact Projection</h4>
            <p className="text-blue-100 mb-4">
              Esta aproximación holística al desarrollo asistido por IA tiene el potencial de reducir el time-to-market 
              en un 70% mientras mantiene calidad enterprise, creando un nuevo estándar en la industria.
            </p>
            <div className="flex items-center space-x-6 text-sm">
              <div>
                <span className="text-blue-200">Projected Market Capture:</span>
                <span className="text-white font-semibold ml-2">15% del TAM en 5 años</span>
              </div>
              <div>
                <span className="text-blue-200">Revenue Potential:</span>
                <span className="text-white font-semibold ml-2">$67.5B</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}