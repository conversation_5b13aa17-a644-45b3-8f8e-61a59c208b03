import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Shield, AlertTriangle, TrendingUp, Zap } from 'lucide-react';

const sections = [
  { id: 'intro', name: 'Introduction', icon: Shield },
  { id: 'strategy', name: 'Strategy', icon: TrendingUp },
  { id: 'swot', name: 'SWOT Analysis', icon: Shield },
  { id: 'pestel', name: 'PESTEL Analysis', icon: AlertTriangle },
  { id: 'porter', name: 'Porter\'s Five Forces', icon: Zap },
  { id: 'catwoe', name: 'CATWOE Analysis', icon: TrendingUp },
  { id: 'gamechanging', name: 'Game Changing Idea', icon: Zap },
];

export function StrategicFramework() {
  const [expandedSection, setExpandedSection] = useState('intro');

  const toggleSection = (sectionId: string) => {
    setExpandedSection(expandedSection === sectionId ? '' : sectionId);
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Strategic Framework</h1>
        <p className="text-gray-600">
          Comprehensive strategic analysis for CodeFlow AI including business strategy, market positioning, 
          competitive analysis, and strategic frameworks to guide decision-making.
        </p>
      </div>

      {/* Introduction */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <button
          onClick={() => toggleSection('intro')}
          className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Introduction</h2>
          </div>
          {expandedSection === 'intro' ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
        </button>
        
        {expandedSection === 'intro' && (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 space-y-4">
              <p className="text-gray-700">
                CodeFlow AI representa una oportunidad transformacional en el mercado de herramientas de desarrollo de software. 
                Esta plataforma de desarrollo asistida por IA está posicionada para capturar una porción significativa del 
                mercado global de herramientas de desarrollo, valorado en $450B.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">Vision Estratégica</h4>
                  <p className="text-blue-700 text-sm">
                    Democratizar el desarrollo de software de alta calidad mediante IA, 
                    eliminando barreras técnicas y acelerando la innovación.
                  </p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-2">Posicionamiento</h4>
                  <p className="text-green-700 text-sm">
                    Líder en automatización inteligente de desarrollo, enfocado en 
                    calidad enterprise y integración seamless.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Strategy */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <button
          onClick={() => toggleSection('strategy')}
          className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-5 h-5 text-green-600" />
            <h2 className="text-xl font-semibold text-gray-900">Strategy</h2>
          </div>
          {expandedSection === 'strategy' ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
        </button>
        
        {expandedSection === 'strategy' && (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Business Strategy</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Freemium model con funcionalidades premium</li>
                    <li>• Enfoque en developers y equipos enterprise</li>
                    <li>• Expansión global progresiva</li>
                    <li>• Partnership estratégicos con cloud providers</li>
                  </ul>
                </div>
                
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Business Framework</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Plataforma SaaS escalable</li>
                    <li>• API-first architecture</li>
                    <li>• Multi-tenant seguro</li>
                    <li>• Marketplace de extensiones</li>
                  </ul>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Requirement Analysis</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Latencia {'<'}100ms en code generation</li>
                    <li>• 99.9% uptime SLA</li>
                    <li>• Soporte para 20+ lenguajes</li>
                    <li>• Compliance SOC2, GDPR</li>
                  </ul>
                </div>
                
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Revenue Streams</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Suscripciones mensuales/anuales</li>
                    <li>• Enterprise licenses</li>
                    <li>• Professional services</li>
                    <li>• Marketplace commissions</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* SWOT Analysis */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <button
          onClick={() => toggleSection('swot')}
          className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">SWOT Analysis</h2>
          </div>
          {expandedSection === 'swot' ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
        </button>
        
        {expandedSection === 'swot' && (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-3 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Strengths
                  </h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Tecnología AI avanzada y propietaria</li>
                    <li>• Equipo con experiencia en ML/AI</li>
                    <li>• Partnerships estratégicos establecidos</li>
                    <li>• Arquitectura escalable y segura</li>
                    <li>• Time-to-market ventajoso</li>
                  </ul>
                </div>
                
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-3 flex items-center">
                    <Zap className="w-4 h-4 mr-2" />
                    Opportunities
                  </h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Crecimiento del mercado AI/ML (85% anual)</li>
                    <li>• Adopción acelerada de low-code/no-code</li>
                    <li>• Demanda de automatización en desarrollo</li>
                    <li>• Expansión a mercados emergentes</li>
                    <li>• Integración con cloud platforms</li>
                  </ul>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="font-semibold text-red-900 mb-3 flex items-center">
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Weaknesses
                  </h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• Dependencia de talento especializado</li>
                    <li>• Altos costos de infraestructura AI</li>
                    <li>• Curva de aprendizaje para usuarios</li>
                    <li>• Limitaciones en legacy systems</li>
                    <li>• Necesidad de capital significativo</li>
                  </ul>
                </div>
                
                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <h4 className="font-semibold text-orange-900 mb-3 flex items-center">
                    <Shield className="w-4 h-4 mr-2" />
                    Threats
                  </h4>
                  <ul className="text-sm text-orange-700 space-y-1">
                    <li>• Competencia de tech giants (Microsoft, Google)</li>
                    <li>• Cambios regulatorios en AI</li>
                    <li>• Preocupaciones de seguridad y privacidad</li>
                    <li>• Saturación del mercado de dev tools</li>
                    <li>• Dependencia de APIs de terceros</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* PESTEL Analysis */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <button
          onClick={() => toggleSection('pestel')}
          className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-5 h-5 text-orange-600" />
            <h2 className="text-xl font-semibold text-gray-900">PESTEL Analysis</h2>
          </div>
          {expandedSection === 'pestel' ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
        </button>
        
        {expandedSection === 'pestel' && (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Political</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Regulaciones AI en desarrollo</li>
                  <li>• Políticas de data sovereignty</li>
                  <li>• Incentivos para tech startups</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Economic</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Crecimiento del sector tech</li>
                  <li>• Inversión en transformación digital</li>
                  <li>• Volatilidad en funding</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Social</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Adopción de AI por developers</li>
                  <li>• Preocupaciones sobre job displacement</li>
                  <li>• Remote work normalization</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Technological</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Avances en LLMs y code generation</li>
                  <li>• Cloud computing evolution</li>
                  <li>• Edge computing adoption</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Environmental</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Presión por green computing</li>
                  <li>• Eficiencia energética en AI</li>
                  <li>• Sustainability reporting</li>
                </ul>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Legal</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• GDPR y data protection</li>
                  <li>• IP y copyright en AI-generated code</li>
                  <li>• Liability frameworks</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Porter's Five Forces */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <button
          onClick={() => toggleSection('porter')}
          className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <Zap className="w-5 h-5 text-purple-600" />
            <h2 className="text-xl font-semibold text-gray-900">Porter's Five Forces Analysis</h2>
          </div>
          {expandedSection === 'porter' ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
        </button>
        
        {expandedSection === 'porter' && (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">Threat of New Entrants</h4>
                    <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded">HIGH</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Barreras moderadas debido a la complejidad técnica, pero el crecimiento del mercado atrae nuevos competidores.
                  </p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    <li>• Capital intensivo para AI infrastructure</li>
                    <li>• Necesidad de talento especializado</li>
                    <li>• Network effects débiles inicialmente</li>
                  </ul>
                </div>

                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">Bargaining Power of Suppliers</h4>
                    <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">HIGH</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Dependencia de pocos proveedores de AI/ML infrastructure y talent pool limitado.
                  </p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    <li>• Cloud providers dominan el mercado</li>
                    <li>• Talent pool limitado en AI/ML</li>
                    <li>• APIs de terceros críticas</li>
                  </ul>
                </div>

                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">Bargaining Power of Buyers</h4>
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">MEDIUM</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Developers tienen opciones pero switching costs están creciendo con integración profunda.
                  </p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    <li>• Múltiples alternativas disponibles</li>
                    <li>• Switching costs moderados</li>
                    <li>• Price sensitivity variable</li>
                  </ul>
                </div>

                <div className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">Threat of Substitutes</h4>
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">LOW</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    Pocas alternativas ofrecen el mismo nivel de automatización y calidad de código.
                  </p>
                  <ul className="text-xs text-gray-500 space-y-1">
                    <li>• Traditional dev tools menos eficientes</li>
                    <li>• Manual coding no escalable</li>
                    <li>• Low-code platforms limitadas</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-purple-900">Competitive Rivalry</h4>
                  <span className="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">HIGH</span>
                </div>
                <p className="text-sm text-purple-700 mb-3">
                  Competencia intensa con jugadores establecidos y nuevos entrantes invirtiendo fuertemente en AI-powered development tools.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                  <div>
                    <strong className="text-purple-900">Major Players:</strong>
                    <ul className="text-purple-700 mt-1 space-y-1">
                      <li>• GitHub Copilot</li>
                      <li>• Tabnine</li>
                      <li>• Replit</li>
                    </ul>
                  </div>
                  <div>
                    <strong className="text-purple-900">Competitive Factors:</strong>
                    <ul className="text-purple-700 mt-1 space-y-1">
                      <li>• Innovation speed</li>
                      <li>• Integration capabilities</li>
                      <li>• Pricing strategies</li>
                    </ul>
                  </div>
                  <div>
                    <strong className="text-purple-900">Differentiation:</strong>
                    <ul className="text-purple-700 mt-1 space-y-1">
                      <li>• Quality focus</li>
                      <li>• Enterprise features</li>
                      <li>• Specialized workflows</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Game Changing Idea */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm p-8 text-white">
        <div className="flex items-center space-x-3 mb-6">
          <Zap className="w-6 h-6" />
          <h2 className="text-2xl font-bold">Game Changing Idea</h2>
        </div>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3">The Core Idea</h3>
            <p className="text-blue-100 text-lg leading-relaxed">
              CodeFlow AI no es solo otra herramienta de code generation. Es la primera plataforma que combina 
              <strong className="text-white"> AI contextual profunda</strong>, 
              <strong className="text-white"> testing automático inteligente</strong>, y 
              <strong className="text-white"> deployment predictivo</strong> en un workflow unificado que aprende 
              continuamente de cada proyecto para optimizar no solo el código, sino todo el ciclo de desarrollo.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <h4 className="font-semibold mb-2">🧠 Contextual AI</h4>
              <p className="text-sm text-blue-100">
                Entiende el contexto completo del proyecto, arquitectura y patrones para generar código que se integra perfectamente.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <h4 className="font-semibold mb-2">🔄 Continuous Learning</h4>
              <p className="text-sm text-blue-100">
                Cada interacción mejora el modelo, adaptándose al estilo del equipo y mejorando la calidad progresivamente.
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <h4 className="font-semibold mb-2">🚀 Predictive Deployment</h4>
              <p className="text-sm text-blue-100">
                Predice y previene issues en producción antes del deployment, garantizando estabilidad enterprise.
              </p>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <h4 className="font-semibold text-lg mb-3">Market Impact Projection</h4>
            <p className="text-blue-100 mb-4">
              Esta aproximación holística al desarrollo asistido por IA tiene el potencial de reducir el time-to-market 
              en un 70% mientras mantiene calidad enterprise, creando un nuevo estándar en la industria.
            </p>
            <div className="flex items-center space-x-6 text-sm">
              <div>
                <span className="text-blue-200">Projected Market Capture:</span>
                <span className="text-white font-semibold ml-2">15% del TAM en 5 años</span>
              </div>
              <div>
                <span className="text-blue-200">Revenue Potential:</span>
                <span className="text-white font-semibold ml-2">$67.5B</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}