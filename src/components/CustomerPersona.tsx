import React, { useState } from 'react';
import { User, Briefcase, Target, MessageSquare, CheckCircle, AlertTriangle } from 'lucide-react';

const personas = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Senior Full-Stack Developer',
    company: 'TechStartup Inc.',
    age: 29,
    location: 'San Francisco, CA',
    background: 'Computer Science graduate with 6 years of experience. Works at a fast-growing fintech startup.',
    challenges: [
      'Spending too much time on repetitive coding tasks',
      'Difficulty maintaining code quality under tight deadlines',
      'Context switching between multiple projects reduces productivity',
      'Manual testing and debugging consumes significant time'
    ],
    goals: [
      'Increase development velocity without sacrificing quality',
      'Focus more time on complex problem-solving and architecture',
      'Reduce time spent on boilerplate code and routine tasks',
      'Improve work-life balance by being more efficient'
    ],
    objections: [
      'Concerned about AI-generated code quality and security',
      'Worried about becoming too dependent on AI tools',
      'Skeptical about AI understanding complex business logic',
      'Price sensitivity for personal/small team usage'
    ],
    offer: [
      'AI assistant that learns coding patterns and style',
      'Automated testing and code review capabilities',
      'Time-saving templates and boilerplate generation',
      'Integration with existing development workflow'
    ],
    identifiers: [
      'Active on GitHub and Stack Overflow',
      'Follows tech influencers on Twitter/LinkedIn',
      'Attends local developer meetups and conferences',
      'Subscribes to programming newsletters and podcasts'
    ],
    demographics: {
      income: '$120K - $150K',
      education: 'Bachelor\'s in Computer Science',
      techStack: 'React, Node.js, Python, AWS',
      experience: '6+ years'
    },
    quotes: [
      '"I spend way too much time writing the same CRUD operations over and over"',
      '"Code reviews take forever and I always find the same types of bugs"',
      '"I want to focus on solving business problems, not fighting with boilerplate"'
    ]
  },
  {
    id: 2,
    name: 'Sarah Rodriguez',
    role: 'Engineering Manager',
    company: 'MediumCorp',
    age: 34,
    location: 'Austin, TX',
    background: 'Former developer turned engineering manager. Leads a team of 8 developers at a mid-size SaaS company.',
    challenges: [
      'Team productivity varies significantly across developers',
      'Code quality inconsistency across team members',
      'Difficulty estimating project timelines accurately',
      'Onboarding new developers takes too long'
    ],
    goals: [
      'Standardize team productivity and code quality',
      'Reduce time-to-productivity for new hires',
      'Improve project predictability and delivery times',
      'Enable team to handle more complex projects'
    ],
    objections: [
      'Concerned about team skill atrophy with AI assistance',
      'Security and compliance requirements are strict',
      'Budget approval process for new tools is complex',
      'Resistance to change from senior team members'
    ],
    offer: [
      'Team analytics and productivity insights',
      'Standardized code patterns and style enforcement',
      'Onboarding acceleration tools',
      'Enterprise security and compliance features'
    ],
    identifiers: [
      'Member of engineering leadership communities',
      'Attends management and technical conferences',
      'Active in company decision-making processes',
      'Evaluates tools for team adoption'
    ],
    demographics: {
      income: '$150K - $180K',
      education: 'Bachelor\'s/Master\'s in Engineering or CS',
      teamSize: '8-15 developers',
      experience: '10+ years total, 3+ in management'
    },
    quotes: [
      '"I need tools that make my entire team more productive, not just individuals"',
      '"Code quality consistency is my biggest challenge with a growing team"',
      '"New hires take 3-6 months to be fully productive - that\'s too long"'
    ]
  },
  {
    id: 3,
    name: 'David Kim',
    role: 'CTO',
    company: 'Enterprise Solutions',
    age: 42,
    location: 'New York, NY',
    background: 'Seasoned technology executive with 15+ years experience. Oversees technology strategy for Fortune 500 company.',
    challenges: [
      'Pressure to accelerate digital transformation initiatives',
      'Rising development costs and talent acquisition difficulties',
      'Need to modernize legacy systems while maintaining stability',
      'Balancing innovation with security and compliance requirements'
    ],
    goals: [
      'Reduce overall development costs and time-to-market',
      'Improve development team efficiency and scalability',
      'Modernize development practices while maintaining quality',
      'Enable faster response to market opportunities'
    ],
    objections: [
      'Concerns about enterprise security and data privacy',
      'Need for extensive compliance and audit trails',
      'Integration complexity with existing enterprise systems',
      'ROI justification for large-scale tool adoption'
    ],
    offer: [
      'Enterprise-grade security and compliance features',
      'ROI analysis and productivity metrics tracking',
      'Scalable deployment across large development teams',
      'Integration with existing enterprise development tools'
    ],
    identifiers: [
      'Speaks at technology and business conferences',
      'Member of CTO and executive technology forums',
      'Influences technology purchasing decisions',
      'Focuses on strategic technology initiatives'
    ],
    demographics: {
      income: '$250K - $400K+',
      education: 'Advanced degree (MBA/MS)',
      companySize: '1000+ employees',
      experience: '15+ years in technology leadership'
    },
    quotes: [
      '"We need to deliver more value faster while maintaining enterprise standards"',
      '"Development velocity is becoming a competitive advantage"',
      '"I need solutions that scale across our entire engineering organization"'
    ]
  },
  {
    id: 4,
    name: 'Maria Gonzalez',
    role: 'Junior Developer',
    company: 'StartupLabs',
    age: 24,
    location: 'Los Angeles, CA',
    background: 'Recent bootcamp graduate with 1 year of professional experience. Works at an early-stage startup.',
    challenges: [
      'Steep learning curve with complex codebases',
      'Imposter syndrome and lack of confidence in code quality',
      'Difficulty debugging complex issues independently',
      'Pressure to deliver while still learning fundamentals'
    ],
    goals: [
      'Accelerate learning and skill development',
      'Gain confidence in code quality and best practices',
      'Contribute meaningfully to team projects',
      'Build a strong foundation for career growth'
    ],
    objections: [
      'Worried that AI tools will hinder learning and skill development',
      'Concerned about job security if AI can replace junior developers',
      'Limited budget for professional tools',
      'Need tools that teach rather than just automate'
    ],
    offer: [
      'Educational features that explain code generation decisions',
      'Best practices guidance and code quality improvement',
      'Learning-focused AI that teaches while assisting',
      'Affordable pricing for individual developers'
    ],
    identifiers: [
      'Active in coding bootcamp and junior developer communities',
      'Frequently uses learning platforms and tutorials',
      'Seeks mentorship and learning opportunities',
      'Price-conscious but willing to invest in career development'
    ],
    demographics: {
      income: '$60K - $85K',
      education: 'Bootcamp/Self-taught/Associate degree',
      experience: '1-2 years',
      careerStage: 'Early career, high growth potential'
    },
    quotes: [
      '"I want tools that help me learn, not just do the work for me"',
      '"Code reviews are intimidating - I never know if my code is good enough"',
      '"I need to prove myself and contribute value to the team"'
    ]
  },
  {
    id: 5,
    name: 'Thomas Anderson',
    role: 'DevOps Engineer',
    company: 'CloudFirst Solutions',
    age: 36,
    location: 'Seattle, WA',
    background: 'Infrastructure specialist with development background. Focuses on CI/CD, automation, and deployment strategies.',
    challenges: [
      'Manual deployment processes prone to human error',
      'Difficulty maintaining consistency across environments',
      'Complex infrastructure configuration management',
      'Integration challenges between development and operations tools'
    ],
    goals: [
      'Automate deployment and infrastructure management',
      'Improve deployment reliability and reduce downtime',
      'Create seamless developer experience for deployments',
      'Implement infrastructure as code best practices'
    ],
    objections: [
      'Concerns about AI-generated infrastructure code security',
      'Need for integration with existing DevOps toolchain',
      'Skeptical about AI understanding complex deployment requirements',
      'Worried about AI creating deployment anti-patterns'
    ],
    offer: [
      'Intelligent deployment automation and rollback capabilities',
      'Infrastructure code generation and optimization',
      'Integration with popular DevOps tools and platforms',
      'Deployment best practices and security scanning'
    ],
    identifiers: [
      'Active in DevOps and cloud computing communities',
      'Attends infrastructure and operations conferences',
      'Evaluates tools for development workflow improvement',
      'Focuses on automation and reliability'
    ],
    demographics: {
      income: '$130K - $160K',
      education: 'Bachelor\'s in CS/Engineering + certifications',
      experience: '8+ years in infrastructure/operations',
      focus: 'Automation, reliability, scalability'
    },
    quotes: [
      '"Every manual deployment is a potential failure point"',
      '"I need tools that understand the complexity of modern infrastructure"',
      '"The gap between dev and ops needs to disappear"'
    ]
  }
];

export function CustomerPersona() {
  const [selectedPersona, setSelectedPersona] = useState(personas[0]);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Customer Personas</h1>
        <p className="text-gray-600">
          Detailed analysis of key customer segments for CodeFlow AI, including demographics, 
          pain points, goals, and behavioral patterns to guide product development and marketing strategy.
        </p>
      </div>

      {/* Persona Selector */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Select Persona</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {personas.map((persona) => (
            <button
              key={persona.id}
              onClick={() => setSelectedPersona(persona)}
              className={`p-4 border rounded-lg text-left transition-all ${
                selectedPersona.id === persona.id
                  ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  selectedPersona.id === persona.id ? 'bg-blue-600' : 'bg-gray-400'
                }`}>
                  <User className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 text-sm">{persona.name}</h3>
                  <p className="text-xs text-gray-600">{persona.role}</p>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Selected Persona Details */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        {/* Persona Header */}
        <div className="p-8 border-b border-gray-100">
          <div className="flex items-start space-x-6">
            <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center">
              <User className="w-10 h-10 text-white" />
            </div>
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedPersona.name}</h2>
              <p className="text-lg text-blue-600 font-medium mb-2">{selectedPersona.role}</p>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="flex items-center">
                  <Briefcase className="w-4 h-4 mr-1" />
                  {selectedPersona.company}
                </span>
                <span>Age: {selectedPersona.age}</span>
                <span>{selectedPersona.location}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Persona Content */}
        <div className="p-8">
          {/* Background */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Background</h3>
            <p className="text-gray-700">{selectedPersona.background}</p>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Challenges */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
                  Challenges
                </h3>
                <div className="space-y-3">
                  {selectedPersona.challenges.map((challenge, index) => (
                    <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-700">{challenge}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Target className="w-5 h-5 text-green-500 mr-2" />
                  Goals
                </h3>
                <div className="space-y-3">
                  {selectedPersona.goals.map((goal, index) => (
                    <div key={index} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                      <p className="text-sm text-green-700">{goal}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Objections & Offer */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2" />
                  Objections
                </h3>
                <div className="space-y-3">
                  {selectedPersona.objections.map((objection, index) => (
                    <div key={index} className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p className="text-sm text-yellow-700">{objection}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <CheckCircle className="w-5 h-5 text-blue-500 mr-2" />
                  What Can You Offer
                </h3>
                <div className="space-y-3">
                  {selectedPersona.offer.map((offer, index) => (
                    <div key={index} className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-700">{offer}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Identifiers & Demographics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Identifiers</h3>
              <div className="space-y-2">
                {selectedPersona.identifiers.map((identifier, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <p className="text-sm text-gray-700">{identifier}</p>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Demographics</h3>
              <div className="space-y-3">
                {Object.entries(selectedPersona.demographics).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm font-medium text-gray-600 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}:
                    </span>
                    <span className="text-sm text-gray-900">{value}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Real Quotes */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <MessageSquare className="w-5 h-5 text-purple-500 mr-2" />
              Real Quotes
            </h3>
            <div className="space-y-4">
              {selectedPersona.quotes.map((quote, index) => (
                <blockquote key={index} className="p-4 bg-purple-50 border-l-4 border-purple-500 italic">
                  <p className="text-purple-700">{quote}</p>
                  <footer className="text-purple-600 text-sm mt-2">— {selectedPersona.name}</footer>
                </blockquote>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Persona Summary */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm p-8 text-white">
        <h2 className="text-2xl font-bold mb-4">Customer Persona Insights</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-2">Target Segments</h3>
            <p className="text-blue-100 text-sm">
              5 key personas spanning from junior developers to CTOs, each with distinct needs and pain points.
            </p>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-2">Common Themes</h3>
            <p className="text-blue-100 text-sm">
              All personas share concerns about productivity, code quality, and time management in development workflows.
            </p>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <h3 className="font-semibold mb-2">Value Differentiation</h3>
            <p className="text-blue-100 text-sm">
              Each persona requires tailored messaging focusing on their specific role responsibilities and success metrics.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}