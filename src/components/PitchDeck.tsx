import React, { useState } from 'react';
import { BarChart3, Download, Eye, FileText, Presentation, ArrowRight, Check, Star, Users, DollarSign, TrendingUp } from 'lucide-react';

type Research = {
  id: string;
  title: string;
  date: string;
  viabilityScore: number;
  industry: string;
  status: 'completed' | 'in-progress';
};

const availableResearches: Research[] = [
  {
    id: '1',
    title: 'CodeFlow AI',
    date: '2024-06-15',
    viabilityScore: 8.4,
    industry: 'Software Development',
    status: 'completed'
  },
  {
    id: '2',
    title: 'EcoTrack Sustainability Platform',
    date: '2024-05-22',
    viabilityScore: 7.8,
    industry: 'Green Technology',
    status: 'completed'
  },
  {
    id: '3',
    title: 'HealthSync Telemedicine App',
    date: '2024-04-10',
    viabilityScore: 8.9,
    industry: 'Healthcare',
    status: 'completed'
  }
];

const pitchDeckSlides = [
  { id: 1, title: 'Problem Statement', description: 'Define the core problem your solution addresses' },
  { id: 2, title: 'Solution Overview', description: 'Present your unique solution and value proposition' },
  { id: 3, title: 'Market Opportunity', description: 'Show market size, growth potential, and target segments' },
  { id: 4, title: 'Business Model', description: 'Explain how you will generate revenue' },
  { id: 5, title: 'Competitive Analysis', description: 'Position against competitors and show advantages' },
  { id: 6, title: 'Go-to-Market Strategy', description: 'Detail your customer acquisition and growth plan' },
  { id: 7, title: 'Financial Projections', description: 'Present revenue forecasts and key metrics' },
  { id: 8, title: 'Team & Execution', description: 'Showcase your team and implementation roadmap' },
  { id: 9, title: 'Funding Requirements', description: 'Specify investment needs and use of funds' },
  { id: 10, title: 'Next Steps', description: 'Call to action and immediate milestones' }
];

export function PitchDeck() {
  const [selectedResearch, setSelectedResearch] = useState<Research | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedDeck, setGeneratedDeck] = useState<any>(null);
  const [currentSlide, setCurrentSlide] = useState(0);

  const handleGeneratePitchDeck = () => {
    if (!selectedResearch) return;
    
    setIsGenerating(true);
    
    // Simulate AI generation process
    setTimeout(() => {
      setGeneratedDeck({
        title: selectedResearch.title,
        slides: pitchDeckSlides.length,
        createdAt: new Date().toISOString(),
        research: selectedResearch
      });
      setIsGenerating(false);
    }, 3000);
  };

  const handleDownloadPDF = () => {
    // Simulate PDF download
    console.log('Downloading pitch deck PDF...');
  };

  const handlePreviewSlide = (slideIndex: number) => {
    setCurrentSlide(slideIndex);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Pitch Deck Generator</h1>
        <p className="text-gray-600">
          Selecciona uno de tus proyectos de investigación y genera automáticamente una presentación profesional 
          con IA que incluye todos los elementos clave para presentar tu idea a inversores.
        </p>
      </div>

      {!generatedDeck ? (
        <>
          {/* Research Selection */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">Seleccionar Proyecto de Investigación</h2>
              <p className="text-gray-600 mt-1">Elige el proyecto para el cual quieres generar el pitch deck</p>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 gap-4">
                {availableResearches.map((research) => (
                  <div
                    key={research.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedResearch?.id === research.id
                        ? 'border-blue-500 bg-blue-50 shadow-sm'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedResearch(research)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">{research.title}</h3>
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
                            <span className="font-bold text-gray-900">{research.viabilityScore.toFixed(1)}</span>
                            <span className="text-sm text-gray-500">/10</span>
                          </div>
                        </div>
                        <div className="mt-1 flex items-center text-sm text-gray-500 space-x-4">
                          <span>{research.industry}</span>
                          <span>{new Date(research.date).toLocaleDateString()}</span>
                        </div>
                      </div>
                      {selectedResearch?.id === research.id && (
                        <Check className="w-5 h-5 text-blue-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Pitch Deck Preview */}
          {selectedResearch && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-semibold text-gray-900">Vista Previa del Pitch Deck</h2>
                <p className="text-gray-600 mt-1">
                  Tu presentación incluirá {pitchDeckSlides.length} diapositivas profesionales basadas en tu investigación
                </p>
              </div>
              
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {pitchDeckSlides.map((slide, index) => (
                    <div key={slide.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <span className="text-blue-600 font-medium text-sm">{index + 1}</span>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 text-sm">{slide.title}</h3>
                          <p className="text-xs text-gray-600 mt-1">{slide.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="mt-8 flex justify-center">
                  <button
                    onClick={handleGeneratePitchDeck}
                    disabled={isGenerating}
                    className={`px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center ${
                      isGenerating ? 'opacity-75 cursor-not-allowed' : ''
                    }`}
                  >
                    {isGenerating ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Generando Presentación...
                      </>
                    ) : (
                      <>
                        <Presentation className="w-5 h-5 mr-2" />
                        Generar Pitch Deck con IA
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        /* Generated Pitch Deck */
        <div className="space-y-6">
          {/* Success Message */}
          <div className="bg-green-50 border border-green-200 rounded-xl p-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <Check className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-green-900">¡Pitch Deck Generado Exitosamente!</h3>
                <p className="text-green-700">
                  Tu presentación para "{generatedDeck.title}" está lista. Incluye {generatedDeck.slides} diapositivas profesionales.
                </p>
              </div>
            </div>
          </div>

          {/* Deck Actions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{generatedDeck.title} - Pitch Deck</h2>
                <p className="text-gray-600">Generado el {new Date(generatedDeck.createdAt).toLocaleDateString()}</p>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={handleDownloadPDF}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Descargar PDF
                </button>
                <button
                  onClick={() => setGeneratedDeck(null)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                >
                  Generar Nuevo
                </button>
              </div>
            </div>

            {/* Slide Preview */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Slide Navigation */}
              <div className="lg:col-span-1">
                <h3 className="font-medium text-gray-900 mb-3">Diapositivas</h3>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {pitchDeckSlides.map((slide, index) => (
                    <button
                      key={slide.id}
                      onClick={() => handlePreviewSlide(index)}
                      className={`w-full text-left p-3 rounded-lg transition-colors ${
                        currentSlide === index
                          ? 'bg-blue-50 border border-blue-200'
                          : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <span className={`text-sm font-medium ${
                          currentSlide === index ? 'text-blue-600' : 'text-gray-500'
                        }`}>
                          {index + 1}
                        </span>
                        <div>
                          <h4 className={`text-sm font-medium ${
                            currentSlide === index ? 'text-blue-900' : 'text-gray-900'
                          }`}>
                            {slide.title}
                          </h4>
                          <p className={`text-xs mt-1 ${
                            currentSlide === index ? 'text-blue-700' : 'text-gray-600'
                          }`}>
                            {slide.description}
                          </p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Slide Preview */}
              <div className="lg:col-span-2">
                <h3 className="font-medium text-gray-900 mb-3">
                  Vista Previa - {pitchDeckSlides[currentSlide].title}
                </h3>
                <div className="bg-gray-100 rounded-lg p-8 min-h-96 flex items-center justify-center">
                  <div className="text-center">
                    <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-gray-700 mb-2">
                      {pitchDeckSlides[currentSlide].title}
                    </h4>
                    <p className="text-gray-600 max-w-md">
                      {pitchDeckSlides[currentSlide].description}
                    </p>
                    <div className="mt-6 grid grid-cols-2 gap-4 text-sm">
                      <div className="bg-white p-3 rounded-lg">
                        <Users className="w-5 h-5 text-blue-500 mx-auto mb-1" />
                        <p className="text-gray-600">Target Market</p>
                        <p className="font-medium">2.8M developers</p>
                      </div>
                      <div className="bg-white p-3 rounded-lg">
                        <DollarSign className="w-5 h-5 text-green-500 mx-auto mb-1" />
                        <p className="text-gray-600">Market Size</p>
                        <p className="font-medium">$450B TAM</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-between mt-4">
                  <button
                    onClick={() => setCurrentSlide(Math.max(0, currentSlide - 1))}
                    disabled={currentSlide === 0}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Anterior
                  </button>
                  <span className="text-sm text-gray-500 self-center">
                    {currentSlide + 1} de {pitchDeckSlides.length}
                  </span>
                  <button
                    onClick={() => setCurrentSlide(Math.min(pitchDeckSlides.length - 1, currentSlide + 1))}
                    disabled={currentSlide === pitchDeckSlides.length - 1}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Siguiente
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}