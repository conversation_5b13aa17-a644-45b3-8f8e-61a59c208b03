import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, CheckCircle, HelpCircle, Send, Loader2, <PERSON>, AlertTriangle } from 'lucide-react';
import { DatabaseService } from '../lib/supabase';
import { QuestionGeneratorService, GeneratedQuestion } from '../lib/ai/questionGenerator';
import { StrategicFrameworkService, StrategicFrameworkProgress } from '../lib/ai/strategicFrameworkService';
import { MVPGeneratorService, MVPGeneratorProgress } from '../lib/ai/mvpGeneratorService';
import { USPGeneratorService, USPGeneratorProgress } from '../lib/ai/uspGeneratorService';
import { CustomerPersonasService, CustomerPersonasProgress } from '../lib/ai/customerPersonasService';
import { FinancesService, FinancesProgress } from '../lib/ai/financesService';
import { GoToMarketService, GoToMarketProgress } from '../lib/ai/goToMarketService';
import { CompetitiveAnalysisService, CompetitiveAnalysisProgress } from '../lib/ai/competitiveAnalysisService';
import { DashboardService, DashboardProgress } from '../lib/ai/dashboardService';
import { AILoadingIndicator } from './AILoadingIndicator';

type ProjectType = 'new-idea' | 'existing-project' | null;

export function NewMarketResearch() {
  const [step, setStep] = useState(1);
  const [projectType, setProjectType] = useState<ProjectType>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [industry, setIndustry] = useState('');
  const [questions, setQuestions] = useState<GeneratedQuestion[]>([]);
  const [isGeneratingQuestions, setIsGeneratingQuestions] = useState(false);
  const [isGeneratingAnalysis, setIsGeneratingAnalysis] = useState(false);
  // Research object ID used for redirection - directly use the research.id when needed
  const [frameworkProgress, setFrameworkProgress] = useState<StrategicFrameworkProgress | null>(null);
  const [mvpProgress, setMvpProgress] = useState<MVPGeneratorProgress | null>(null);
  const [uspProgress, setUspProgress] = useState<USPGeneratorProgress | null>(null);
  const [personasProgress, setPersonasProgress] = useState<CustomerPersonasProgress | null>(null);
  const [financesProgress, setFinancesProgress] = useState<FinancesProgress | null>(null);
  const [gtmProgress, setGtmProgress] = useState<GoToMarketProgress | null>(null);
  const [competitiveProgress, setCompetitiveProgress] = useState<CompetitiveAnalysisProgress | null>(null);
  const [dashboardProgress, setDashboardProgress] = useState<DashboardProgress | null>(null);

  // Overall progress state for sequential execution
  const [overallProgress, setOverallProgress] = useState({
    currentMessage: '',
    completed: 0,
    total: 8 // 7 analysis steps + 1 dashboard
  });

  const handleProjectTypeSelect = (type: ProjectType) => {
    setProjectType(type);
    setStep(2);
  };

  const handleDescriptionSubmit = async () => {
    if (!title.trim() || !description.trim()) return;
    
    setIsGeneratingQuestions(true);
    try {
      const questionService = new QuestionGeneratorService();
      // Ensure projectType is defined
      if (!projectType) {
        throw new Error('Project type must be selected');
      }
      
      const generatedQuestions = await questionService.generateQuestions(
        projectType, // First parameter is projectType
        description,
        industry
      );
      
      setQuestions(generatedQuestions.questions);
      setStep(3);
    } catch (error) {
      console.error('Error generating questions:', error);
    } finally {
      setIsGeneratingQuestions(false);
    }
  };

  const handleQuestionsSubmit = () => {
    setStep(4);
  };

  // Track the errors that occurred during generation
  const [generationErrors, setGenerationErrors] = useState<{
    section: string;
    error: string;
  }[]>([]);
  
  // Helper function for type-safe error handling
  const formatErrorMessage = (error: unknown): string => {
    return error instanceof Error ? error.message : String(error);
  };
  
  const handleGenerateAnalysis = async () => {
    if (!projectType) return;
    
    setIsGeneratingAnalysis(true);
    setGenerationErrors([]);

    // Initialize overall progress
    setOverallProgress({
      currentMessage: 'Initializing analysis...',
      completed: 0,
      total: 8 // 7 analysis steps + 1 dashboard
    });
    
    try {
      // Create the market research record without questions
      // Prepare project data in the format expected by DatabaseService
      const projectData = {
        title,
        description,
        industry,
        project_type: projectType, // Using correct field name
        status: 'in-progress' as const, // Using correct literal type
      };

      // First create the market research record
      const research = await DatabaseService.createMarketResearch(projectData);

      // Then create the questions in the separate research_questions table
      const questionTexts = questions.map(q => {
        // Include answers if they exist
        const answer = (q as any).answer || undefined;
        return answer ? `${q.question}::${answer}` : q.question;
      });

      await DatabaseService.createResearchQuestions(research.id, questionTexts);

      console.log(`Created market research with ID ${research.id}`);

      // Prepare complete project data including answered questions for AI services
      const completeProjectData = {
        research: research,
        questions: questions.map(q => ({
          question: q.question,
          answer: (q as any).answer || '',
          category: q.category,
          importance: q.importance
        }))
      };

      // Create all services first
      const frameworkService = new StrategicFrameworkService(research.id, completeProjectData, setFrameworkProgress);
      const mvpService = new MVPGeneratorService(research.id, completeProjectData, setMvpProgress);
      const uspService = new USPGeneratorService(research.id, completeProjectData, setUspProgress);
      const personasService = new CustomerPersonasService(research.id, completeProjectData, setPersonasProgress);
      const financesService = new FinancesService(research.id, completeProjectData, setFinancesProgress);
      const gtmService = new GoToMarketService(research.id, completeProjectData, setGtmProgress);
      const competitiveService = new CompetitiveAnalysisService(research.id, completeProjectData, setCompetitiveProgress);
      
      // Define generation tasks with service name and generation function in sequential order
      const generationTasks = [
        {
          name: 'Strategic Framework',
          service: frameworkService,
          task: () => frameworkService.generateCompleteFramework()
        },
        {
          name: 'MVP Framework',
          service: mvpService,
          task: () => mvpService.generateCompleteMVPFramework()
        },
        {
          name: 'USP Analysis',
          service: uspService,
          task: () => uspService.generateCompleteUSPFramework()
        },
        {
          name: 'Customer Personas',
          service: personasService,
          task: () => personasService.generateCustomerPersonas()
        },
        {
          name: 'Financial Analysis',
          service: financesService,
          task: () => financesService.generateCompleteFinancialAnalysis()
        },
        {
          name: 'Go-to-Market Strategy',
          service: gtmService,
          task: () => gtmService.generateCompleteGoToMarketStrategy()
        },
        {
          name: 'Competitive Analysis',
          service: competitiveService,
          task: () => competitiveService.generateCompleteCompetitiveAnalysis()
        }
      ];

      // Execute tasks sequentially instead of in parallel
      console.log('Starting sequential generation of all strategic sections');
      const errors: { section: string; error: string }[] = [];

      for (let i = 0; i < generationTasks.length; i++) {
        const currentTask = generationTasks[i];

        try {
          console.log(`Starting ${currentTask.name} (${i + 1}/${generationTasks.length})`);

          // Update overall progress
          setOverallProgress(prev => ({
            ...prev,
            currentMessage: `Generating ${currentTask.name}...`,
            completed: i,
            total: generationTasks.length + 1 // +1 for dashboard
          }));

          await currentTask.task();
          console.log(`Completed ${currentTask.name}`);

          // Update progress after completion
          setOverallProgress(prev => ({
            ...prev,
            currentMessage: `Completed ${currentTask.name}`,
            completed: i + 1,
            total: generationTasks.length + 1
          }));

        } catch (error) {
          console.error(`Error in ${currentTask.name}:`, error);
          errors.push({
            section: currentTask.name,
            error: formatErrorMessage(error)
          });
        }
      }
      
      if (errors.length > 0) {
        console.warn(`${errors.length} analysis sections failed:`, errors);
        setGenerationErrors(errors);
      }

      // Generate Dashboard even if some analyses failed (final step)
      console.log('Generating dashboard with available data');

      // Update progress for dashboard generation
      setOverallProgress(prev => ({
        ...prev,
        currentMessage: 'Generating Dashboard (Final Step)...',
        completed: generationTasks.length,
        total: generationTasks.length + 1
      }));

      const dashboardService = new DashboardService(
        research.id,
        completeProjectData,
        setDashboardProgress
      );

      try {
        await dashboardService.generateCompleteDashboard();
        console.log('Dashboard generation completed');

        // Update final progress
        setOverallProgress(prev => ({
          ...prev,
          currentMessage: 'Market Research Complete!',
          completed: generationTasks.length + 1,
          total: generationTasks.length + 1
        }));
        
        // Redirect to the research page after completion
        setTimeout(() => {
          window.location.href = `/research/${research.id}`;
        }, 3000); // Extended to 3 seconds to give users time to see errors if any
      } catch (dashboardError) {
        console.error('Error generating dashboard:', dashboardError);
        setGenerationErrors(prev => [
          ...prev, 
          { 
            section: 'Dashboard', 
            error: formatErrorMessage(dashboardError)
          }
        ]);
        
        // Still redirect but with longer delay so users can see the error
        setTimeout(() => {
          window.location.href = `/research/${research.id}`;
        }, 5000);
      }

    } catch (error) {
      console.error('Error in overall analysis process:', error);
      setGenerationErrors(prev => [
        ...prev, 
        { 
          section: 'Overall Process', 
          error: formatErrorMessage(error)
        }
      ]);
    } finally {
      setIsGeneratingAnalysis(false);
    }
  };

  // Use the overall progress state for sequential execution
  const progress = overallProgress;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Create New Market Research
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Let our AI analyze your project and provide comprehensive market insights
        </p>
      </div>

      {/* Progress Steps */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div className="flex items-center justify-between mb-8 relative">
          {[1, 2, 3, 4].map((stepNumber) => (
            <div key={stepNumber} className="flex flex-col items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                step >= stepNumber ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'
              }`}>
                {step > stepNumber ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <span>{stepNumber}</span>
                )}
              </div>
              <span className={`text-sm mt-2 ${
                step >= stepNumber ? 'text-blue-600 font-medium' : 'text-gray-500'
              }`}>
                {stepNumber === 1 && "Project Type"}
                {stepNumber === 2 && "Description"}
                {stepNumber === 3 && "Questions"}
                {stepNumber === 4 && "Generate"}
              </span>
            </div>
          ))}
          <div className="absolute left-0 right-0 h-1 bg-gray-200 -z-10" style={{ top: '1.25rem' }}>
            <div 
              className="h-full bg-blue-600 transition-all duration-300" 
              style={{ width: `${(step - 1) * 33.33}%` }}
            ></div>
          </div>
        </div>

        {/* Step 1: Project Type Selection */}
        {step === 1 && (
          <div className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-900 text-center">
              What type of project are you working on?
            </h2>
            <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              <button
                onClick={() => handleProjectTypeSelect('new-idea')}
                className="p-8 border-2 border-gray-200 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all group"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200">
                    <Brain className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">New Idea</h3>
                  <p className="text-gray-600">
                    I have a new business idea or concept that I want to validate and research
                  </p>
                </div>
              </button>
              
              <button
                onClick={() => handleProjectTypeSelect('existing-project')}
                className="p-8 border-2 border-gray-200 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all group"
              >
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Existing Project</h3>
                  <p className="text-gray-600">
                    I have an existing business or project that I want to analyze and improve
                  </p>
                </div>
              </button>
            </div>
          </div>
        )}

        {/* Step 2: Project Description */}
        {step === 2 && (
          <div className="space-y-6 max-w-2xl mx-auto">
            <h2 className="text-2xl font-semibold text-gray-900 text-center">
              Tell us about your {projectType === 'new-idea' ? 'idea' : 'project'}
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project Title
                </label>
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter a descriptive title for your project"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe your project in detail. What problem does it solve? Who is your target audience?"
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Industry (Optional)
                </label>
                <input
                  type="text"
                  value={industry}
                  onChange={(e) => setIndustry(e.target.value)}
                  placeholder="e.g., Technology, Healthcare, E-commerce"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex justify-center">
              <button
                onClick={handleDescriptionSubmit}
                disabled={!title.trim() || !description.trim() || isGeneratingQuestions}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isGeneratingQuestions ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    Generating Questions...
                  </>
                ) : (
                  <>
                    Continue
                    <ArrowRight className="w-5 h-5" />
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Step 3: Questions */}
        {step === 3 && (
          <div className="space-y-6 max-w-4xl mx-auto">
            <div className="text-center">
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                Research Questions
              </h2>
              <p className="text-gray-600">
                Our AI has generated key questions to help analyze your project. You can edit or add answers.
              </p>
            </div>

            <div className="space-y-4">
              {questions.map((question, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <div className="flex items-start gap-3 mb-3">
                    <HelpCircle className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                    <h3 className="font-medium text-gray-900">{question.question}</h3>
                  </div>
                  <textarea
                    value={(question as any).answer || ''}
                    onChange={(e) => {
                      const updatedQuestions = [...questions];
                      // Use type assertion to fix TypeScript error
                      updatedQuestions[index] = { 
                        ...(question as any), 
                        answer: e.target.value 
                      } as GeneratedQuestion;
                      setQuestions(updatedQuestions);
                    }}
                    placeholder="Your answer (optional - AI will research this if left blank)"
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              ))}
            </div>

            <div className="flex justify-center">
              <button
                onClick={handleQuestionsSubmit}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
              >
                Continue to Analysis
                <ArrowRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}

        {/* Step 4: Generate Analysis */}
        {step === 4 && (
          <div className="space-y-6 max-w-2xl mx-auto text-center">
            <h2 className="text-2xl font-semibold text-gray-900">
              Ready to Generate Your Market Research
            </h2>
            <p className="text-gray-600">
              Our AI will analyze your project and generate comprehensive insights including strategic framework, MVP planning, competitive analysis, and more.
            </p>

            {!isGeneratingAnalysis ? (
              <button
                onClick={handleGenerateAnalysis}
                className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 flex items-center gap-2 mx-auto text-lg font-medium"
              >
                <Send className="w-6 h-6" />
                Generate Market Research
              </button>
            ) : (
              <div className="space-y-6">
                <AILoadingIndicator 
                  message={progress.currentMessage || "Initializing analysis..."}
                  progress={(progress.completed / progress.total) * 100}
                />
                <div className="text-sm text-gray-500">
                  Completed {progress.completed} of {progress.total} analysis sections
                </div>
                
                {/* Show errors if any sections failed */}
                {generationErrors.length > 0 && (
                  <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg max-w-lg mx-auto">
                    <div className="flex items-center gap-2 text-yellow-700 mb-2">
                      <AlertTriangle className="w-5 h-5" />
                      <h4 className="font-medium">Some sections encountered issues</h4>
                    </div>
                    <ul className="text-sm text-left text-yellow-700 space-y-1">
                      {generationErrors.map((error, index) => (
                        <li key={index}>
                          <span className="font-medium">{error.section}:</span> {error.error}
                        </li>
                      ))}
                    </ul>
                    <p className="text-xs text-yellow-600 mt-2">
                      Your research will continue with available data. Incomplete sections can be regenerated later.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}