import React, { useState, useEffect } from 'react';
import { BarChart3, Calendar, Star, ArrowRight, Download, Loader2, AlertCircle } from 'lucide-react';
import { DatabaseService } from '../lib/supabase';

type Research = {
  id: string;
  title: string;
  created_at: string;
  project_type: string;
  description: string;
  status?: 'completed' | 'in-progress';
};

const selectResearch = (research: Research) => {
  // Store selected research in localStorage for global access
  localStorage.setItem('selectedResearch', JSON.stringify(research));

  // Dispatch event to notify other components
  window.dispatchEvent(new CustomEvent('researchSelected', { detail: research }));

  // Navigate to dashboard to show the selected research
  window.dispatchEvent(new CustomEvent('navigateToTab', { detail: 'dashboard' }));
};

export function YourResearches() {
  const [researches, setResearches] = useState<Research[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadResearches();
  }, []);

  const loadResearches = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await DatabaseService.getUserMarketResearches();
      setResearches(data || []);
    } catch (err) {
      console.error('Error loading researches:', err);
      setError('Failed to load your researches. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNew = () => {
    window.dispatchEvent(new CustomEvent('navigateToTab', { detail: 'new-research' }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading your researches...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Researches</h3>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={loadResearches}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Your Market Research Projects</h1>
        <p className="text-gray-600">
          View and manage all your market research projects. Select any project to view its detailed analysis.
        </p>
      </div>

      {/* Research List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900">
            Recent Projects ({researches.length})
          </h2>
        </div>

        {researches.length === 0 ? (
          <div className="p-12 text-center">
            <BarChart3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Research Projects Yet</h3>
            <p className="text-gray-600 mb-6">
              Create your first market research project to get started with AI-powered business analysis.
            </p>
            <button
              onClick={handleCreateNew}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center mx-auto"
            >
              Create Your First Research
              <ArrowRight className="w-4 h-4 ml-2" />
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {researches.map((research) => (
              <div
                key={research.id}
                className="p-6 hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => selectResearch(research)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900">{research.title}</h3>
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                        Completed
                      </span>
                    </div>
                    <div className="mt-1 flex items-center text-sm text-gray-500 space-x-4">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        <span>{new Date(research.created_at).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center">
                        <BarChart3 className="w-4 h-4 mr-1" />
                        <span>{research.project_type}</span>
                      </div>
                    </div>
                    {research.description && (
                      <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                        {research.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        selectResearch(research);
                      }}
                      className="flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium"
                    >
                      View Analysis <ArrowRight className="w-4 h-4 ml-1" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create New Button */}
      {researches.length > 0 && (
        <div className="flex justify-center">
          <button
            onClick={handleCreateNew}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
          >
            Create New Research Project
            <ArrowRight className="w-4 h-4 ml-2" />
          </button>
        </div>
      )}
    </div>
  );
}