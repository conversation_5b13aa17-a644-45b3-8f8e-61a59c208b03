import React from 'react';
import { BarChart3, Calendar, Star, ArrowRight, Download } from 'lucide-react';

type Research = {
  id: string;
  title: string;
  date: string;
  viabilityScore: number;
  industry: string;
  status: 'completed' | 'in-progress';
};

const researchData: Research[] = [
  {
    id: '1',
    title: 'CodeFlow AI',
    date: '2024-06-15',
    viabilityScore: 8.4,
    industry: 'Software Development',
    status: 'completed'
  },
  {
    id: '2',
    title: 'EcoTrack Sustainability Platform',
    date: '2024-05-22',
    viabilityScore: 7.8,
    industry: 'Green Technology',
    status: 'completed'
  },
  {
    id: '3',
    title: 'HealthSync Telemedicine App',
    date: '2024-04-10',
    viabilityScore: 8.9,
    industry: 'Healthcare',
    status: 'completed'
  },
  {
    id: '4',
    title: 'FinLedger Blockchain Solution',
    date: '2024-06-28',
    viabilityScore: 6.5,
    industry: 'Fintech',
    status: 'in-progress'
  }
];

const generatePDF = async (research: Research) => {
  try {
    // Dynamic import to avoid bundling issues
    const jsPDF = (await import('jspdf')).default;
    const html2canvas = (await import('html2canvas')).default;
    
    const pdf = new jsPDF();
    
    // Add title
    pdf.setFontSize(20);
    pdf.text(research.title, 20, 30);
    
    // Add subtitle
    pdf.setFontSize(12);
    pdf.text('Market Research Report', 20, 45);
    
    // Add date
    pdf.text(`Generated on: ${new Date(research.date).toLocaleDateString()}`, 20, 60);
    
    // Add viability score
    pdf.text(`Viability Score: ${research.viabilityScore}/10`, 20, 75);
    
    // Add industry
    pdf.text(`Industry: ${research.industry}`, 20, 90);
    
    // Add status
    pdf.text(`Status: ${research.status}`, 20, 105);
    
    // Add sample content sections
    pdf.setFontSize(14);
    pdf.text('Executive Summary', 20, 130);
    pdf.setFontSize(10);
    pdf.text('This market research report provides comprehensive analysis of the business opportunity,', 20, 145);
    pdf.text('competitive landscape, target market, and strategic recommendations.', 20, 155);
    
    pdf.setFontSize(14);
    pdf.text('Market Analysis', 20, 180);
    pdf.setFontSize(10);
    pdf.text('The target market shows strong growth potential with favorable conditions for entry.', 20, 195);
    pdf.text('Key market drivers include technological advancement and changing consumer preferences.', 20, 205);
    
    pdf.setFontSize(14);
    pdf.text('Competitive Landscape', 20, 230);
    pdf.setFontSize(10);
    pdf.text('Analysis of direct and indirect competitors reveals opportunities for differentiation', 20, 245);
    pdf.text('through unique value propositions and strategic positioning.', 20, 255);
    
    // Save the PDF
    pdf.save(`${research.title.replace(/\s+/g, '_')}_Market_Research.pdf`);
  } catch (error) {
    console.error('Error generating PDF:', error);
    alert('Error generating PDF. Please try again.');
  }
};

export function YourResearches() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Your Market Research Projects</h1>
        <p className="text-gray-600">
          View and manage all your market research projects. Select any project to view its detailed analysis.
        </p>
      </div>

      {/* Research List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900">Recent Projects</h2>
        </div>
        <div className="divide-y divide-gray-100">
          {researchData.map((research) => (
            <div key={research.id} className="p-6 hover:bg-gray-50 transition-colors cursor-pointer">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-medium text-gray-900">{research.title}</h3>
                    {research.status === 'in-progress' && (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                        In Progress
                      </span>
                    )}
                  </div>
                  <div className="mt-1 flex items-center text-sm text-gray-500 space-x-4">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      <span>{new Date(research.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center">
                      <BarChart3 className="w-4 h-4 mr-1" />
                      <span>{research.industry}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-1 mb-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
                    <span className="font-bold text-gray-900">{research.viabilityScore.toFixed(1)}</span>
                    <span className="text-sm text-gray-500">/10</span>
                  </div>
                  <div className="flex flex-col space-y-2">
                    <button className="flex items-center text-sm text-blue-600 hover:text-blue-800">
                      View Details <ArrowRight className="w-4 h-4 ml-1" />
                    </button>
                    <button 
                      onClick={(e) => {
                        e.stopPropagation();
                        generatePDF(research);
                      }}
                      className="flex items-center text-sm text-green-600 hover:text-green-800"
                    >
                      Download PDF <Download className="w-4 h-4 ml-1" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create New Button */}
      <div className="flex justify-center">
        <button className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          Create New Research Project
          <ArrowRight className="w-4 h-4 ml-2" />
        </button>
      </div>
    </div>
  );
}