import React from 'react';
import { <PERSON>, Loader2, Search, BarChart3 } from 'lucide-react';

interface AILoadingIndicatorProps {
  message?: string;
  type?: 'thinking' | 'researching' | 'analyzing' | 'generating';
  progress?: number;
}

export function AILoadingIndicator({ 
  message = 'Processing...', 
  type = 'thinking',
  progress 
}: AILoadingIndicatorProps) {
  const getIcon = () => {
    switch (type) {
      case 'researching':
        return <Search className="w-5 h-5" />;
      case 'analyzing':
        return <BarChart3 className="w-5 h-5" />;
      case 'generating':
        return <Brain className="w-5 h-5" />;
      default:
        return <Loader2 className="w-5 h-5 animate-spin" />;
    }
  };

  const getColor = () => {
    switch (type) {
      case 'researching':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'analyzing':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'generating':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className={`flex items-center space-x-3 p-4 border rounded-lg ${getColor()}`}>
      <div className="flex-shrink-0">
        {getIcon()}
      </div>
      <div className="flex-1">
        <p className="text-sm font-medium">{message}</p>
        {progress !== undefined && (
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-current h-2 rounded-full transition-all duration-300" 
                style={{ width: `${progress}%` }}
              />
            </div>
            <p className="text-xs mt-1">{progress}% complete</p>
          </div>
        )}
      </div>
    </div>
  );
}