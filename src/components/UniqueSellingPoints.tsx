import React from 'react';
import { Star, TrendingUp, Alert<PERSON>riangle, Check, X, Trophy, Target } from 'lucide-react';

const uspAnalysis = [
  {
    usp: 'AI-Powered Full-Cycle Development',
    strength: 'High',
    uniqueness: 'Very High',
    market_demand: 'High',
    sustainability: 'High',
    status: 'Winning'
  },
  {
    usp: '70% Development Time Reduction',
    strength: 'High',
    uniqueness: 'Medium',
    market_demand: 'Very High',
    sustainability: 'Medium',
    status: 'Winning'
  },
  {
    usp: 'Enterprise-Grade Security',
    strength: 'Medium',
    uniqueness: 'Low',
    market_demand: 'High',
    sustainability: 'High',
    status: 'Risky'
  },
  {
    usp: 'Multi-Language Support',
    strength: 'Medium',
    uniqueness: 'Low',
    market_demand: 'Medium',
    sustainability: 'Low',
    status: 'Losing'
  }
];

const competitors = [
  {
    name: 'CodeFlow AI',
    ai_integration: 9,
    development_speed: 9,
    code_quality: 8,
    enterprise_features: 8,
    pricing: 7,
    market_position: 'Challenger'
  },
  {
    name: 'GitHub Copilot',
    ai_integration: 8,
    development_speed: 7,
    code_quality: 7,
    enterprise_features: 9,
    pricing: 8,
    market_position: 'Leader'
  },
  {
    name: 'Tabnine',
    ai_integration: 7,
    development_speed: 6,
    code_quality: 6,
    enterprise_features: 6,
    pricing: 8,
    market_position: 'Follower'
  },
  {
    name: 'Replit',
    ai_integration: 6,
    development_speed: 8,
    code_quality: 5,
    enterprise_features: 4,
    pricing: 9,
    market_position: 'Niche'
  }
];

export function UniqueSellingPoints() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Winning': return 'text-green-600 bg-green-100';
      case 'Risky': return 'text-yellow-600 bg-yellow-100';
      case 'Losing': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'Very High': return 'bg-green-500';
      case 'High': return 'bg-green-400';
      case 'Medium': return 'bg-yellow-400';
      case 'Low': return 'bg-red-400';
      default: return 'bg-gray-400';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Unique Selling Points</h1>
        <p className="text-gray-600">
          Comprehensive analysis of CodeFlow AI's unique value propositions, competitive positioning, 
          and recommendations for strengthening market differentiation.
        </p>
      </div>

      {/* Unique Selling Propositions */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Unique Selling Propositions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="p-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Primary USP</h3>
              <p className="text-blue-100 text-sm mb-4">
                La única plataforma que combina AI contextual profunda con testing automático 
                y deployment predictivo en un workflow unificado.
              </p>
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4" />
                <span className="text-sm font-medium">Diferenciación clave del mercado</span>
              </div>
            </div>

            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">Supporting USPs</h4>
              <ul className="space-y-2 text-sm text-gray-700">
                <li className="flex items-center space-x-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>70% reducción en tiempo de desarrollo</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>Calidad enterprise garantizada</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>Integración seamless con workflows existentes</span>
                </li>
                <li className="flex items-center space-x-2">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>Continuous learning y adaptación</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Value Propositions</h4>
              <div className="space-y-3">
                <div>
                  <h5 className="font-medium text-green-800">Para Developers</h5>
                  <p className="text-sm text-green-700">
                    Elimina tareas repetitivas y errores comunes, permitiendo enfoque en lógica compleja y creatividad.
                  </p>
                </div>
                <div>
                  <h5 className="font-medium text-green-800">Para CTOs</h5>
                  <p className="text-sm text-green-700">
                    Reduce costos de desarrollo, mejora time-to-market y garantiza calidad consistente.
                  </p>
                </div>
                <div>
                  <h5 className="font-medium text-green-800">Para Empresas</h5>
                  <p className="text-sm text-green-700">
                    Acelera transformación digital, reduce riesgos técnicos y mejora ROI en tecnología.
                  </p>
                </div>
              </div>
            </div>

            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h4 className="font-semibold text-orange-900 mb-2">Competitive Moats</h4>
              <ul className="space-y-1 text-sm text-orange-700">
                <li>• Proprietary AI models trained on enterprise patterns</li>
                <li>• Network effects from team collaboration data</li>
                <li>• High switching costs due to workflow integration</li>
                <li>• Continuous improvement through usage data</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* USP Acid Test */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">The Early USP Acid Test</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">USP</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Strength</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Uniqueness</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Market Demand</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Sustainability</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
              </tr>
            </thead>
            <tbody>
              {uspAnalysis.map((usp, index) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-3 px-4 font-medium text-gray-900">{usp.usp}</td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.strength)}`}>
                      {usp.strength}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.uniqueness)}`}>
                      {usp.uniqueness}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.market_demand)}`}>
                      {usp.market_demand}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.sustainability)}`}>
                      {usp.sustainability}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-2">
                      {usp.status === 'Winning' && <Trophy className="w-4 h-4 text-green-500" />}
                      {usp.status === 'Risky' && <AlertTriangle className="w-4 h-4 text-yellow-500" />}
                      {usp.status === 'Losing' && <X className="w-4 h-4 text-red-500" />}
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.status)}`}>
                        {usp.status}
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* What Makes You Unique */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">What Makes You Unique?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-3">
              <Target className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold text-blue-900 mb-2">Contextual Intelligence</h3>
            <p className="text-sm text-blue-700">
              Nuestro AI entiende no solo el código, sino el contexto completo del proyecto, 
              arquitectura y patrones específicos del equipo.
            </p>
          </div>

          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-3">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold text-green-900 mb-2">Full-Cycle Automation</h3>
            <p className="text-sm text-green-700">
              Única plataforma que automatiza desde code generation hasta testing y deployment 
              en un workflow integrado.
            </p>
          </div>

          <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-3">
              <Star className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold text-purple-900 mb-2">Predictive Quality</h3>
            <p className="text-sm text-purple-700">
              Predice y previene issues en producción antes del deployment, 
              garantizando estabilidad enterprise.
            </p>
          </div>
        </div>
      </div>

      {/* USP Examples */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Good USP Examples</h2>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Check className="w-4 h-4 text-green-600" />
                <h3 className="font-semibold text-green-900">GitHub</h3>
              </div>
              <p className="text-sm text-green-700 mb-2">
                "How people build software"
              </p>
              <p className="text-xs text-green-600">
                Simple, memorable, focuses on community and collaboration
              </p>
            </div>

            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Check className="w-4 h-4 text-green-600" />
                <h3 className="font-semibold text-green-900">Stripe</h3>
              </div>
              <p className="text-sm text-green-700 mb-2">
                "Internet infrastructure for businesses"
              </p>
              <p className="text-xs text-green-600">
                Positions as essential foundation, not just payment processor
              </p>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="w-4 h-4 text-blue-600" />
                <h3 className="font-semibold text-blue-900">CodeFlow AI</h3>
              </div>
              <p className="text-sm text-blue-700 mb-2">
                "The first AI that thinks like your development team"
              </p>
              <p className="text-xs text-blue-600">
                Emphasizes contextual understanding and team integration
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Bad USP Examples</h2>
          <div className="space-y-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <X className="w-4 h-4 text-red-600" />
                <h3 className="font-semibold text-red-900">Generic Example</h3>
              </div>
              <p className="text-sm text-red-700 mb-2">
                "Best-in-class AI development platform"
              </p>
              <p className="text-xs text-red-600">
                Too generic, no specific value, meaningless claims
              </p>
            </div>

            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <X className="w-4 h-4 text-red-600" />
                <h3 className="font-semibold text-red-900">Feature-focused</h3>
              </div>
              <p className="text-sm text-red-700 mb-2">
                "AI-powered code generator with 20+ languages"
              </p>
              <p className="text-xs text-red-600">
                Lists features instead of benefits, not unique
              </p>
            </div>

            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <X className="w-4 h-4 text-red-600" />
                <h3 className="font-semibold text-red-900">Unbelievable</h3>
              </div>
              <p className="text-sm text-red-700 mb-2">
                "Replace all developers with AI"
              </p>
              <p className="text-xs text-red-600">
                Unrealistic promise, alienates target audience
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Ranking Competitors USP Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Ranking Competitors USP Analysis</h2>
        <p className="text-gray-600 mb-6">
          Comparison matrix ranking key criteria from 1-10 (higher is better)
        </p>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">Company</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">AI Integration</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">Development Speed</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">Code Quality</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">Enterprise Features</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">Pricing</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Position</th>
              </tr>
            </thead>
            <tbody>
              {competitors.map((competitor, index) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-3 px-4 font-medium text-gray-900">{competitor.name}</td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.ai_integration * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.ai_integration}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.development_speed * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.development_speed}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.code_quality * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.code_quality}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.enterprise_features * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.enterprise_features}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.pricing * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.pricing}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      competitor.market_position === 'Leader' ? 'bg-green-100 text-green-800' :
                      competitor.market_position === 'Challenger' ? 'bg-blue-100 text-blue-800' :
                      competitor.market_position === 'Follower' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {competitor.market_position}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}