import React, { useState } from 'react';
import { Rocket, CheckCircle, ArrowRight, Users, Code, BarChart3, CreditCard, Megaphone, Shield, Star, Building, Zap } from 'lucide-react';

type Research = {
  id: string;
  title: string;
  date: string;
  viabilityScore: number;
  industry: string;
  status: 'completed' | 'in-progress';
};

const availableResearches: Research[] = [
  {
    id: '1',
    title: 'CodeFlow AI',
    date: '2024-06-15',
    viabilityScore: 8.4,
    industry: 'Software Development',
    status: 'completed'
  },
  {
    id: '2',
    title: 'EcoTrack Sustainability Platform',
    date: '2024-05-22',
    viabilityScore: 7.8,
    industry: 'Green Technology',
    status: 'completed'
  },
  {
    id: '3',
    title: 'HealthSync Telemedicine App',
    date: '2024-04-10',
    viabilityScore: 8.9,
    industry: 'Healthcare',
    status: 'completed'
  }
];

const services = [
  {
    id: 'development',
    name: 'Desarrollo de Software',
    description: 'Desarrollo completo desde 0 con las mejores tecnologías',
    icon: Code,
    features: ['Frontend & Backend', 'Mobile Apps', 'APIs & Integrations', 'Testing & QA'],
    price: 'Desde $15,000',
    timeline: '8-16 semanas'
  },
  {
    id: 'design',
    name: 'Diseño UX/UI',
    description: 'Diseño profesional centrado en el usuario',
    icon: Building,
    features: ['User Research', 'Wireframes & Prototypes', 'Visual Design', 'Design System'],
    price: 'Desde $5,000',
    timeline: '4-6 semanas'
  },
  {
    id: 'marketing',
    name: 'Marketing Digital',
    description: 'Estrategia completa de marketing y crecimiento',
    icon: Megaphone,
    features: ['Brand Strategy', 'Content Marketing', 'SEO & SEM', 'Social Media'],
    price: 'Desde $3,000/mes',
    timeline: 'Ongoing'
  },
  {
    id: 'accounting',
    name: 'Contabilidad & Legal',
    description: 'Servicios contables y legales para tu startup',
    icon: CreditCard,
    features: ['Company Formation', 'Tax Planning', 'Financial Reports', 'Legal Compliance'],
    price: 'Desde $500/mes',
    timeline: 'Ongoing'
  },
  {
    id: 'analytics',
    name: 'Analytics & BI',
    description: 'Implementación de analytics y business intelligence',
    icon: BarChart3,
    features: ['Data Tracking', 'Custom Dashboards', 'KPI Monitoring', 'Growth Analytics'],
    price: 'Desde $2,000',
    timeline: '2-4 semanas'
  },
  {
    id: 'security',
    name: 'Seguridad & Compliance',
    description: 'Implementación de seguridad y cumplimiento normativo',
    icon: Shield,
    features: ['Security Audit', 'GDPR Compliance', 'Data Protection', 'Penetration Testing'],
    price: 'Desde $3,000',
    timeline: '3-5 semanas'
  }
];

const questions = [
  {
    id: 'timeline',
    question: '¿Cuál es tu timeline ideal para el lanzamiento?',
    type: 'select',
    options: ['3 meses', '6 meses', '9 meses', '12 meses', 'Flexible']
  },
  {
    id: 'budget',
    question: '¿Cuál es tu presupuesto aproximado?',
    type: 'select',
    options: ['$10,000 - $25,000', '$25,000 - $50,000', '$50,000 - $100,000', '$100,000+', 'Por definir']
  },
  {
    id: 'priority',
    question: '¿Cuál es tu prioridad principal?',
    type: 'select',
    options: ['Lanzar rápido (MVP)', 'Calidad premium', 'Costo optimizado', 'Escalabilidad', 'Todas las anteriores']
  },
  {
    id: 'team',
    question: '¿Tienes equipo técnico interno?',
    type: 'select',
    options: ['Sí, equipo completo', 'Equipo parcial', 'Solo yo', 'Busco formar equipo', 'Prefiero outsourcing']
  },
  {
    id: 'experience',
    question: '¿Cuál es tu experiencia previa con startups?',
    type: 'select',
    options: ['Primera vez', 'He lanzado 1-2 proyectos', 'Emprendedor serial', 'Experiencia corporativa', 'Otro']
  }
];

export function LaunchIdea() {
  const [selectedResearch, setSelectedResearch] = useState<Research | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [answers, setAnswers] = useState<{[key: string]: string}>({});
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [showProposal, setShowProposal] = useState(false);

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev => 
      prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const handleAnswerQuestion = (answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questions[currentQuestion].id]: answer
    }));
    
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      setCurrentStep(4);
    }
  };

  const handleGenerateProposal = () => {
    setShowProposal(true);
  };

  const getTotalEstimate = () => {
    const selectedServiceObjects = services.filter(service => selectedServices.includes(service.id));
    const developmentCost = selectedServices.includes('development') ? 25000 : 0;
    const designCost = selectedServices.includes('design') ? 7500 : 0;
    const analyticsCost = selectedServices.includes('analytics') ? 3000 : 0;
    const securityCost = selectedServices.includes('security') ? 4500 : 0;
    
    return developmentCost + designCost + analyticsCost + securityCost;
  };

  const getEstimatedTimeline = () => {
    if (selectedServices.includes('development')) return '12-20 semanas';
    if (selectedServices.includes('design')) return '6-10 semanas';
    return '4-8 semanas';
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Launch Your Idea</h1>
        <p className="text-gray-600">
          Selecciona uno de tus proyectos y nosotros nos encargamos de crear tu producto desde cero. 
          Ofrecemos servicios completos de desarrollo, diseño, marketing, contabilidad y más para 
          llevarte del concepto al lanzamiento exitoso.
        </p>
      </div>

      {/* Progress Steps */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <div className="flex items-center justify-between mb-8">
          {[1, 2, 3, 4].map((stepNumber) => (
            <div key={stepNumber} className="flex flex-col items-center">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                currentStep >= stepNumber ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'
              }`}>
                {currentStep > stepNumber ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <span>{stepNumber}</span>
                )}
              </div>
              <span className={`text-sm mt-2 ${
                currentStep >= stepNumber ? 'text-blue-600 font-medium' : 'text-gray-500'
              }`}>
                {stepNumber === 1 && "Seleccionar Proyecto"}
                {stepNumber === 2 && "Elegir Servicios"}
                {stepNumber === 3 && "Cuestionario"}
                {stepNumber === 4 && "Propuesta"}
              </span>
            </div>
          ))}
        </div>

        {/* Step 1: Select Research */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Selecciona tu Proyecto</h2>
            <p className="text-gray-600">
              Elige el proyecto de investigación que quieres convertir en realidad
            </p>
            
            <div className="grid grid-cols-1 gap-4">
              {availableResearches.map((research) => (
                <div
                  key={research.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedResearch?.id === research.id
                      ? 'border-blue-500 bg-blue-50 shadow-sm'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedResearch(research)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-medium text-gray-900">{research.title}</h3>
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
                          <span className="font-bold text-gray-900">{research.viabilityScore.toFixed(1)}</span>
                          <span className="text-sm text-gray-500">/10</span>
                        </div>
                      </div>
                      <div className="mt-1 flex items-center text-sm text-gray-500 space-x-4">
                        <span>{research.industry}</span>
                        <span>{new Date(research.date).toLocaleDateString()}</span>
                      </div>
                    </div>
                    {selectedResearch?.id === research.id && (
                      <CheckCircle className="w-5 h-5 text-blue-600" />
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={() => setCurrentStep(2)}
                disabled={!selectedResearch}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Continuar <ArrowRight className="w-4 h-4 ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Step 2: Select Services */}
        {currentStep === 2 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Selecciona los Servicios</h2>
            <p className="text-gray-600">
              Elige los servicios que necesitas para lanzar tu proyecto "{selectedResearch?.title}"
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {services.map((service) => {
                const Icon = service.icon;
                const isSelected = selectedServices.includes(service.id);
                
                return (
                  <div
                    key={service.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50 shadow-sm'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                    }`}
                    onClick={() => handleServiceToggle(service.id)}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        isSelected ? 'bg-blue-600' : 'bg-gray-100'
                      }`}>
                        <Icon className={`w-5 h-5 ${isSelected ? 'text-white' : 'text-gray-600'}`} />
                      </div>
                      {isSelected && <CheckCircle className="w-5 h-5 text-blue-600" />}
                    </div>
                    
                    <h3 className="font-medium text-gray-900 mb-1">{service.name}</h3>
                    <p className="text-sm text-gray-600 mb-3">{service.description}</p>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Precio:</span>
                        <span className="font-medium text-gray-900">{service.price}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Timeline:</span>
                        <span className="font-medium text-gray-900">{service.timeline}</span>
                      </div>
                    </div>
                    
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <ul className="text-xs text-gray-600 space-y-1">
                        {service.features.map((feature, index) => (
                          <li key={index} className="flex items-center">
                            <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                );
              })}
            </div>
            
            {selectedServices.length > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">Resumen de Servicios Seleccionados</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-blue-700">Servicios:</span>
                    <span className="font-medium text-blue-900 ml-2">{selectedServices.length}</span>
                  </div>
                  <div>
                    <span className="text-blue-700">Estimado:</span>
                    <span className="font-medium text-blue-900 ml-2">${getTotalEstimate().toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-blue-700">Timeline:</span>
                    <span className="font-medium text-blue-900 ml-2">{getEstimatedTimeline()}</span>
                  </div>
                </div>
              </div>
            )}
            
            <div className="flex justify-between">
              <button
                onClick={() => setCurrentStep(1)}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Atrás
              </button>
              <button
                onClick={() => setCurrentStep(3)}
                disabled={selectedServices.length === 0}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Continuar <ArrowRight className="w-4 h-4 ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Step 3: Questions */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Cuestionario de Proyecto</h2>
              <span className="text-sm text-gray-500">Pregunta {currentQuestion + 1} de {questions.length}</span>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
              <h3 className="font-medium text-blue-900 mb-4">{questions[currentQuestion].question}</h3>
              
              <div className="space-y-3">
                {questions[currentQuestion].options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleAnswerQuestion(option)}
                    className="w-full text-left p-3 bg-white border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
                  >
                    {option}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="flex justify-between">
              <button
                onClick={() => {
                  if (currentQuestion > 0) {
                    setCurrentQuestion(currentQuestion - 1);
                  } else {
                    setCurrentStep(2);
                  }
                }}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Atrás
              </button>
            </div>
          </div>
        )}

        {/* Step 4: Proposal */}
        {currentStep === 4 && !showProposal && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Generar Propuesta Personalizada</h2>
            
            <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
              <h3 className="font-medium text-gray-900 mb-4">Resumen del Proyecto</h3>
              
              <div className="space-y-4">
                <div>
                  <span className="text-sm font-medium text-gray-500">Proyecto:</span>
                  <p className="text-gray-900">{selectedResearch?.title}</p>
                </div>
                
                <div>
                  <span className="text-sm font-medium text-gray-500">Servicios Seleccionados:</span>
                  <div className="mt-1 flex flex-wrap gap-2">
                    {selectedServices.map(serviceId => {
                      const service = services.find(s => s.id === serviceId);
                      return (
                        <span key={serviceId} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                          {service?.name}
                        </span>
                      );
                    })}
                  </div>
                </div>
                
                <div>
                  <span className="text-sm font-medium text-gray-500">Respuestas del Cuestionario:</span>
                  <div className="mt-1 space-y-1">
                    {Object.entries(answers).map(([key, value]) => {
                      const question = questions.find(q => q.id === key);
                      return (
                        <div key={key} className="text-sm">
                          <span className="text-gray-700">{question?.question}</span>
                          <span className="text-gray-900 font-medium ml-2">{value}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-between">
              <button
                onClick={() => setCurrentStep(3)}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Atrás
              </button>
              <button
                onClick={handleGenerateProposal}
                className="px-8 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center"
              >
                <Rocket className="w-5 h-5 mr-2" />
                Generar Propuesta Personalizada
              </button>
            </div>
          </div>
        )}

        {/* Generated Proposal */}
        {showProposal && (
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-xl p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-green-900">¡Propuesta Generada!</h3>
                  <p className="text-green-700">
                    Hemos creado una propuesta personalizada para "{selectedResearch?.title}"
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-xl p-8">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Propuesta de Desarrollo</h2>
                <p className="text-gray-600">{selectedResearch?.title}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Zap className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-900">Inversión Total</h3>
                  <p className="text-2xl font-bold text-blue-600">${getTotalEstimate().toLocaleString()}</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <Users className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-900">Timeline</h3>
                  <p className="text-2xl font-bold text-green-600">{getEstimatedTimeline()}</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <Star className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-purple-900">Viabilidad</h3>
                  <p className="text-2xl font-bold text-purple-600">{selectedResearch?.viabilityScore}/10</p>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Servicios Incluidos</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedServices.map(serviceId => {
                      const service = services.find(s => s.id === serviceId);
                      if (!service) return null;
                      const Icon = service.icon;
                      
                      return (
                        <div key={serviceId} className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex items-center space-x-3 mb-2">
                            <Icon className="w-5 h-5 text-blue-600" />
                            <h4 className="font-medium text-gray-900">{service.name}</h4>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{service.description}</p>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">{service.timeline}</span>
                            <span className="font-medium text-gray-900">{service.price}</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Próximos Pasos</h3>
                  <ol className="space-y-2 text-sm text-gray-700">
                    <li className="flex items-start">
                      <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">1</span>
                      <span>Revisión y aprobación de la propuesta</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">2</span>
                      <span>Firma del contrato y pago inicial (30%)</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">3</span>
                      <span>Kick-off meeting y planificación detallada</span>
                    </li>
                    <li className="flex items-start">
                      <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">4</span>
                      <span>Inicio del desarrollo con entregas semanales</span>
                    </li>
                  </ol>
                </div>

                <div className="flex justify-center space-x-4">
                  <button className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    Aceptar Propuesta
                  </button>
                  <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                    Solicitar Modificaciones
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}