import React, { useState } from 'react';
import { useEffect } from 'react';
import { BarChart3, Target, Users, DollarSign, TrendingUp, Lightbulb, PieChart, Briefcase, Crown, Settings, X, Check, ChevronDown, Building, User, Menu, ChevronRight, MoreVertical, MessageCircle, RefreshCw, Share, Edit, Send, FileText } from 'lucide-react';
import { AuthModal } from './components/AuthModal';
import ErrorBoundary from './components/ErrorBoundary';
import { DatabaseService } from './lib/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';
import { Dashboard } from './components/Dashboard';
import { StrategicFramework } from './components/StrategicFramework';
import { PathToMVP } from './components/PathToMVP';
import { UniqueSellingPoints } from './components/UniqueSellingPoints';
import { CustomerPersona } from './components/CustomerPersona';
import { Finances } from './components/Finances';
import { GoToMarket } from './components/GoToMarket';
import { CompetitiveAnalysis } from './components/CompetitiveAnalysis';
import { YourResearches } from './components/YourResearches';
import { NewMarketResearch } from './components/NewMarketResearch';
import { Settings as SettingsPage } from './components/Settings';
import { PitchDeck } from './components/PitchDeck';
import { LaunchIdea } from './components/LaunchIdea';
import { RulesGenerator } from './components/RulesGenerator';

const platformTabs = [
  { id: 'researches', name: 'Your Researches', icon: BarChart3, active: false },
  { id: 'new-research', name: 'New Market Research', icon: Lightbulb, active: false },
];

const analysisTabs = [
  { id: 'dashboard', name: 'Dashboard', icon: BarChart3, active: true },
  { id: 'strategic', name: 'Strategic Framework', icon: Target, active: false },
  { id: 'mvp', name: 'Path to MVP', icon: TrendingUp, active: false },
  { id: 'usp', name: 'Unique Selling Points', icon: Lightbulb, active: false },
  { id: 'personas', name: 'Customer Personas', icon: Users, active: false },
  { id: 'finances', name: 'Finances', icon: DollarSign, active: false },
  { id: 'gtm', name: 'Go-to-Market', icon: PieChart, active: false },
  { id: 'competitive', name: 'Competitive Analysis', icon: Briefcase, active: false },
];

const toolsTabs = [
  { id: 'pitch-deck', name: 'Pitch Deck', icon: BarChart3, active: false },
  { id: 'launch-idea', name: 'Launch Your Idea', icon: TrendingUp, active: false },
  { id: 'rules-generator', name: 'Rules Generator', icon: FileText, active: false },
];
const subscriptionPlans = [
  {
    name: 'Free',
    price: '$0',
    period: 'forever',
    features: ['3 market researches per month', 'Basic analysis', 'Email support'],
    current: true
  },
  {
    name: 'Pro',
    price: '$29',
    period: 'month',
    features: ['Unlimited market researches', 'Advanced analysis', 'Priority support', 'Export to PDF', 'Team collaboration'],
    current: false,
    popular: true
  },
  {
    name: 'Enterprise',
    price: '$99',
    period: 'month',
    features: ['Everything in Pro', 'Custom branding', 'API access', 'Dedicated account manager', 'Advanced integrations'],
    current: false
  }
];
// Combine all tabs for easy lookup
const allTabs = [...platformTabs, ...analysisTabs, ...toolsTabs];

// Component to show when no research is selected
const NoResearchSelected = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md">
      <BarChart3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
      <h3 className="text-xl font-bold text-gray-900 mb-2">No Research Selected</h3>
      <p className="text-gray-600 mb-6">
        Please select a market research project to access the analysis tools and view detailed insights.
      </p>
      <div className="space-y-3">
        <button
          onClick={() => window.dispatchEvent(new CustomEvent('navigateToTab', { detail: 'researches' }))}
          className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          View Your Researches
        </button>
        <button
          onClick={() => window.dispatchEvent(new CustomEvent('navigateToTab', { detail: 'new-research' }))}
          className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Create New Research
        </button>
      </div>
    </div>
  </div>
);

const accounts = [
  {
    id: 'personal',
    name: 'Personal Account',
    type: 'personal',
    email: '<EMAIL>',
    icon: User,
    current: true
  },
  {
    id: 'acme-corp',
    name: 'Acme Corporation',
    type: 'corporate',
    email: '<EMAIL>',
    icon: Building,
    current: false
  },
  {
    id: 'tech-startup',
    name: 'Tech Startup Inc.',
    type: 'corporate',
    email: '<EMAIL>',
    icon: Building,
    current: false
  }
];

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authLoading, setAuthLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showActionsDropdown, setShowActionsDropdown] = useState(false);
  const [showChatbot, setShowChatbot] = useState(false);
  const [chatMessages, setChatMessages] = useState([
    { id: 1, text: "¡Hola! Soy tu asistente de investigación de mercado. ¿En qué puedo ayudarte?", sender: "bot" }
  ]);
  const [chatInput, setChatInput] = useState('');
  const [currentAccount, setCurrentAccount] = useState(accounts[0]);
  const [selectedResearch, setSelectedResearch] = useState<any>(null);

  // Check authentication status on app load
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await DatabaseService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('Error checking auth status:', error);
      } finally {
        setAuthLoading(false);
      }
    };

    checkAuth();

    // Listen for auth state changes
    const { data: { subscription } } = DatabaseService.onAuthStateChange((user) => {
      setUser(user);
      setAuthLoading(false);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  // Listen for navigation events from other components
  useEffect(() => {
    const handleNavigateToTab = (event: CustomEvent) => {
      setActiveTab(event.detail);
    };

    const handleResearchSelected = (event: CustomEvent) => {
      setSelectedResearch(event.detail);
    };

    window.addEventListener('navigateToTab', handleNavigateToTab as EventListener);
    window.addEventListener('researchSelected', handleResearchSelected as EventListener);

    // Load selected research from localStorage on app start
    const savedResearch = localStorage.getItem('selectedResearch');
    if (savedResearch) {
      try {
        setSelectedResearch(JSON.parse(savedResearch));
      } catch (error) {
        console.error('Error parsing saved research:', error);
        localStorage.removeItem('selectedResearch');
      }
    }

    return () => {
      window.removeEventListener('navigateToTab', handleNavigateToTab as EventListener);
      window.removeEventListener('researchSelected', handleResearchSelected as EventListener);
    };
  }, []);

  const handleSignOut = async () => {
    try {
      await DatabaseService.signOut();
      setUser(null);
      setShowUserDropdown(false);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleAuthSuccess = () => {
    setShowAuthModal(false);
  };

  const handleAccountSwitch = (account: typeof accounts[0]) => {
    setCurrentAccount(account);
    setShowAccountDropdown(false);
  };

  const getBreadcrumbs = () => {
    const currentTab = allTabs.find(tab => tab.id === activeTab);
    if (!currentTab) return ['Dashboard'];
    
    if (platformTabs.some(tab => tab.id === activeTab)) {
      return ['Platform', currentTab.name];
    } else if (analysisTabs.some(tab => tab.id === activeTab)) {
      return ['Business Analysis', currentTab.name];
    } else if (activeTab === 'settings') {
      return ['Settings'];
    }
    return [currentTab.name];
  };

  const handleSendMessage = () => {
    if (chatInput.trim()) {
      const newMessage = {
        id: chatMessages.length + 1,
        text: chatInput,
        sender: "user"
      };
      setChatMessages([...chatMessages, newMessage]);
      setChatInput('');
      
      // Simulate bot response
      setTimeout(() => {
        const botResponse = {
          id: chatMessages.length + 2,
          text: "Gracias por tu pregunta. Estoy aquí para ayudarte con tu investigación de mercado. ¿Podrías ser más específico sobre lo que necesitas?",
          sender: "bot"
        };
        setChatMessages(prev => [...prev, botResponse]);
      }, 1000);
    }
  };

  // Show loading screen while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show authentication modal if user is not logged in
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome to Kotler</h1>
            <p className="text-gray-600">Your AI-powered market research platform</p>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Get Started</h2>
            <p className="text-gray-600 mb-6">
              Sign in to access your dashboard and start conducting comprehensive market research with AI assistance.
            </p>
            <button
              onClick={() => setShowAuthModal(true)}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Sign In / Sign Up
            </button>
          </div>
        </div>
        
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          onAuthSuccess={handleAuthSuccess}
        />
      </div>
    );
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'researches':
        return <YourResearches />;
      case 'new-research':
        return <NewMarketResearch />;
      case 'dashboard':
        return (
          <ErrorBoundary
            fallback={
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center bg-white p-8 rounded-xl shadow-md max-w-md">
                  <div className="text-red-500 mx-auto mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Error en el Dashboard</h3>
                  <p className="text-gray-600 mb-6">Hubo un problema al mostrar el dashboard. Esto puede ocurrir si la síntesis del análisis no se ha generado correctamente.</p>
                  <button 
                    onClick={() => window.location.reload()} 
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Reintentar
                  </button>
                </div>
              </div>
            }
          >
            <Dashboard selectedResearch={selectedResearch} />
          </ErrorBoundary>
        );
      case 'strategic':
        return selectedResearch ? <StrategicFramework selectedResearch={selectedResearch} /> : <NoResearchSelected />;
      case 'mvp':
        return selectedResearch ? <PathToMVP selectedResearch={selectedResearch} /> : <NoResearchSelected />;
      case 'usp':
        return selectedResearch ? <UniqueSellingPoints selectedResearch={selectedResearch} /> : <NoResearchSelected />;
      case 'personas':
        return selectedResearch ? <CustomerPersona selectedResearch={selectedResearch} /> : <NoResearchSelected />;
      case 'finances':
        return selectedResearch ? <Finances selectedResearch={selectedResearch} /> : <NoResearchSelected />;
      case 'gtm':
        return selectedResearch ? <GoToMarket selectedResearch={selectedResearch} /> : <NoResearchSelected />;
      case 'competitive':
        return selectedResearch ? <CompetitiveAnalysis selectedResearch={selectedResearch} /> : <NoResearchSelected />;
      case 'pitch-deck':
        return <PitchDeck />;
      case 'launch-idea':
        return <LaunchIdea />;
      case 'rules-generator':
        return <RulesGenerator />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-row">
      {/* Sidebar */}
      <div className={`w-64 bg-white shadow-lg border-r flex-shrink-0 h-screen fixed flex flex-col transition-transform duration-300 ${
        showSidebar ? 'translate-x-0' : '-translate-x-full'
      }`}>
        {/* Logo */}
        <div className="p-4 border-b">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Kotler</h1>
              <span className="text-xs text-gray-500">Market Research Platform</span>
            </div>
          </div>
        </div>
        
        {/* Navigation */}
        <nav className="py-3 flex-1 overflow-y-auto custom-scrollbar relative">
          {/* Account Dropdown */}
          <div className="px-2 mb-4">
            <div className="relative">
              <button
                onClick={() => setShowAccountDropdown(!showAccountDropdown)}
                className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                    currentAccount.type === 'personal' ? 'bg-blue-100' : 'bg-purple-100'
                  }`}>
                    <currentAccount.icon className={`w-4 h-4 ${
                      currentAccount.type === 'personal' ? 'text-blue-600' : 'text-purple-600'
                    }`} />
                  </div>
                  <div className="text-left">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {currentAccount.name}
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      {currentAccount.email}
                    </div>
                  </div>
                </div>
                <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${
                  showAccountDropdown ? 'rotate-180' : ''
                }`} />
              </button>
              
              {/* Dropdown Menu */}
              {showAccountDropdown && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="py-1">
                    {accounts.map((account) => (
                      <button
                        key={account.id}
                        onClick={() => handleAccountSwitch(account)}
                        className={`w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-gray-50 transition-colors ${
                          currentAccount.id === account.id ? 'bg-blue-50' : ''
                        }`}
                      >
                        <div className={`w-6 h-6 rounded flex items-center justify-center ${
                          account.type === 'personal' ? 'bg-blue-100' : 'bg-purple-100'
                        }`}>
                          <account.icon className={`w-3 h-3 ${
                            account.type === 'personal' ? 'text-blue-600' : 'text-purple-600'
                          }`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className={`text-sm font-medium truncate ${
                            currentAccount.id === account.id ? 'text-blue-900' : 'text-gray-900'
                          }`}>
                            {account.name}
                          </div>
                          <div className={`text-xs truncate ${
                            currentAccount.id === account.id ? 'text-blue-600' : 'text-gray-500'
                          }`}>
                            {account.email}
                          </div>
                        </div>
                        {currentAccount.id === account.id && (
                          <Check className="w-4 h-4 text-blue-600" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <div className="space-y-4">
            {/* Platform Section */}
            <div>
              <h3 className="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Platform</h3>
              <div className="px-2 space-y-0.5">
                {platformTabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center space-x-2.5 px-3 py-2 w-full rounded-lg text-sm font-medium transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-50 text-blue-600'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon className={`w-4 h-4 ${activeTab === tab.id ? 'text-blue-500' : 'text-gray-400'}`} />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </div>
            </div>
            
            {/* Business Analysis Section - Only show when research is selected */}
            {selectedResearch && (
              <div>
                <h3 className="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">
                  Business Analysis
                </h3>
                <div className="px-2 mb-2">
                  <div className="px-3 py-2 bg-green-50 border border-green-200 rounded-lg">
                    <div className="text-xs font-medium text-green-800 truncate">
                      {selectedResearch.title}
                    </div>
                    <div className="text-xs text-green-600">
                      Selected Research
                    </div>
                  </div>
                </div>
                <div className="px-2 space-y-0.5">
                  {analysisTabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`flex items-center space-x-2.5 px-3 py-2 w-full rounded-lg text-sm font-medium transition-colors ${
                          activeTab === tab.id
                            ? 'bg-blue-50 text-blue-600'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        }`}
                      >
                        <Icon className={`w-4 h-4 ${activeTab === tab.id ? 'text-blue-500' : 'text-gray-400'}`} />
                        <span>{tab.name}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Message when no research is selected */}
            {!selectedResearch && (
              <div className="px-4">
                <div className="px-3 py-4 bg-gray-50 border border-gray-200 rounded-lg text-center">
                  <BarChart3 className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <div className="text-xs font-medium text-gray-600 mb-1">
                    No Research Selected
                  </div>
                  <div className="text-xs text-gray-500 mb-3">
                    Select a research to access analysis tools
                  </div>
                  <button
                    onClick={() => setActiveTab('researches')}
                    className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View Your Researches
                  </button>
                </div>
              </div>
            )}
            
            {/* Tools Section */}
            <div>
              <h3 className="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Tools</h3>
              <div className="px-2 space-y-0.5">
                {toolsTabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center space-x-2.5 px-3 py-2 w-full rounded-lg text-sm font-medium transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-50 text-blue-600'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon className={`w-4 h-4 ${activeTab === tab.id ? 'text-blue-500' : 'text-gray-400'}`} />
                      <span>{tab.name}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </nav>
        
        {/* Bottom Section */}
        <div className="p-3 border-t border-gray-200 space-y-3">
          {/* Plan Status Card */}
          <div className="bg-gray-50 rounded-lg p-2.5">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-900">Free Plan</span>
              <Crown className="w-4 h-4 text-gray-400" />
            </div>
            <div className="text-xs text-gray-600">
              <div className="flex justify-between mb-1">
                <span>Researches used:</span>
                <span className="font-medium">2/3</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div className="bg-blue-600 h-1.5 rounded-full" style={{ width: '66%' }}></div>
              </div>
            </div>
          </div>
          
          {/* Upgrade Button */}
          <button 
            onClick={() => setShowUpgradeModal(true)}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg py-2 px-3 text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center"
          >
            <Crown className="w-4 h-4 mr-2" />
            Upgrade to a paid plan
          </button>
          
          {/* Settings Button */}
          <button 
            onClick={() => setActiveTab('settings')}
            className={`w-full flex items-center space-x-2.5 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeTab === 'settings'
                ? 'bg-blue-50 text-blue-600'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }`}
          >
            <Settings className={`w-4 h-4 ${activeTab === 'settings' ? 'text-blue-500' : 'text-gray-400'}`} />
            <span>Settings</span>
          </button>
          
          {/* User Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowUserDropdown(!showUserDropdown)}
              className="w-full flex items-center space-x-3 p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user?.user_metadata?.full_name?.charAt(0) || user?.email?.charAt(0).toUpperCase() || 'U'}
                </span>
              </div>
              <div className="flex-1 text-left">
                <div className="text-sm font-medium text-gray-900">
                  {user?.user_metadata?.full_name || 'User'}
                </div>
                <div className="text-xs text-gray-500">{user?.email}</div>
              </div>
              <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${
                showUserDropdown ? 'rotate-180' : ''
              }`} />
            </button>
            
            {/* User Dropdown Menu */}
            {showUserDropdown && (
              <div className="absolute bottom-full left-0 right-0 mb-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => {
                      setActiveTab('settings');
                      setShowUserDropdown(false);
                    }}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors"
                  >
                    <Settings className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">Billing</span>
                  </button>
                  
                  <button
                    onClick={() => {
                      // Toggle theme logic here
                      setShowUserDropdown(false);
                    }}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors"
                  >
                    <div className="w-4 h-4 bg-gray-800 rounded-full"></div>
                    <span className="text-sm text-gray-700">Apariencia</span>
                    <span className="text-xs text-gray-500 ml-auto">Oscuro</span>
                  </button>
                  
                  <button
                    onClick={() => {
                      // Language selection logic here
                      setShowUserDropdown(false);
                    }}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors"
                  >
                    <div className="w-4 h-4 rounded border border-gray-300 flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">ES</span>
                    </div>
                    <span className="text-sm text-gray-700">Idioma</span>
                    <span className="text-xs text-gray-500 ml-auto">Español</span>
                  </button>
                  
                  <div className="border-t border-gray-100 my-1"></div>
                  
                  <button
                    onClick={() => {
                      handleSignOut();
                      setShowUserDropdown(false);
                    }}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-red-50 transition-colors text-red-600"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span className="text-sm">Log off</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Main Content */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ${showSidebar ? 'ml-64' : 'ml-0'}`}>
        {/* Top Bar */}
        <div className="bg-white border-b border-gray-200 px-6 py-3 flex items-center justify-between">
          {/* Left Section */}
          <div className="flex items-center space-x-4">
            {/* Sidebar Toggle */}
            <button
              onClick={() => setShowSidebar(!showSidebar)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Menu className="w-5 h-5 text-gray-600" />
            </button>
            
            {/* Breadcrumbs */}
            <nav className="flex items-center space-x-2 text-sm">
              {getBreadcrumbs().map((crumb, index) => (
                <div key={index} className="flex items-center space-x-2">
                  {index > 0 && <ChevronRight className="w-4 h-4 text-gray-400" />}
                  <span className={index === getBreadcrumbs().length - 1 
                    ? "text-gray-900 font-medium" 
                    : "text-gray-500 hover:text-gray-700 cursor-pointer"
                  }>
                    {crumb}
                  </span>
                </div>
              ))}
            </nav>
          </div>
          
          {/* Right Section */}
          <div className="flex items-center space-x-3">
            {/* Actions Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowActionsDropdown(!showActionsDropdown)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <MoreVertical className="w-5 h-5 text-gray-600" />
              </button>
              
              {showActionsDropdown && (
                <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="py-1">
                    <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors">
                      <RefreshCw className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-700">Regenerar</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors">
                      <Share className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-700">Compartir</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors">
                      <Edit className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-700">Editar</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
            
            {/* Chatbot Toggle */}
            <button
              onClick={() => setShowChatbot(!showChatbot)}
              className={`p-2 rounded-lg transition-colors ${
                showChatbot 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <MessageCircle className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        {/* Content Area */}
        <main className="flex-1 overflow-auto p-6">
          {/* Page Title */}
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-gray-800">
              {allTabs.find(tab => tab.id === activeTab)?.name || 'Dashboard'}
            </h2>
          </div>
          
          <div className="max-w-6xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>
      
      {/* Chatbot Panel */}
      <div className={`fixed right-0 top-0 h-full w-80 bg-white border-l border-gray-200 shadow-lg transform transition-transform duration-300 z-40 ${
        showChatbot ? 'translate-x-0' : 'translate-x-full'
      }`}>
        {/* Chatbot Header */}
        <div className="p-4 border-b border-gray-200 bg-blue-600 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <MessageCircle className="w-4 h-4" />
              </div>
              <div>
                <h3 className="font-medium">Asistente de Investigación</h3>
                <p className="text-xs text-blue-100">En línea</p>
              </div>
            </div>
            <button
              onClick={() => setShowChatbot(false)}
              className="p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4" style={{ height: 'calc(100vh - 140px)' }}>
          {chatMessages.map((message) => (
            <div key={message.id} className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                message.sender === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                {message.text}
              </div>
            </div>
          ))}
        </div>
        
        {/* Chat Input */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex space-x-2">
            <input
              type="text"
              value={chatInput}
              onChange={(e) => setChatInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Escribe tu pregunta..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
            <button
              onClick={handleSendMessage}
              disabled={!chatInput.trim()}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
      
      {/* Overlay for chatbot */}
      {showChatbot && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-25 z-30"
          onClick={() => setShowChatbot(false)}
        />
      )}
      
      {/* Upgrade Modal */}
      {showUpgradeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">Choose Your Plan</h2>
                <button 
                  onClick={() => setShowUpgradeModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              <p className="text-gray-600 mt-2">Unlock the full potential of your market research</p>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {subscriptionPlans.map((plan, index) => (
                  <div 
                    key={index} 
                    className={`relative border rounded-xl p-6 ${
                      plan.popular 
                        ? 'border-blue-500 shadow-lg scale-105' 
                        : plan.current 
                          ? 'border-green-500 bg-green-50' 
                          : 'border-gray-200'
                    }`}
                  >
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}
                    
                    {plan.current && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                          Current Plan
                        </span>
                      </div>
                    )}
                    
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                      <div className="flex items-baseline justify-center">
                        <span className="text-3xl font-bold text-gray-900">{plan.price}</span>
                        <span className="text-gray-500 ml-1">/{plan.period}</span>
                      </div>
                    </div>
                    
                    <ul className="space-y-3 mb-6">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <Check className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    
                    <button 
                      className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                        plan.current
                          ? 'bg-green-100 text-green-800 cursor-default'
                          : plan.popular
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                      disabled={plan.current}
                    >
                      {plan.current ? 'Current Plan' : 'Upgrade Now'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onAuthSuccess={handleAuthSuccess}
      />
    </div>
  );
}

export default App;